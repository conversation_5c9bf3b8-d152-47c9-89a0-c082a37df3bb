from .import views
from django.urls import path


urlpatterns = [
    path('register', views.create_user, name="create_account"),
    path('account/activate/<str:uidb64>/<str:token>', views.account_activation, name="account-activation"),
    path('profile', views.user_profile, name="user_profile"),
    path('company-address', views.company_address, name="company_address"),
    path('profile/edit/<str:id>', views.update_profile, name="update_profile"),
    path('login', views.user_login, name="user_login"),
    path('logout', views.user_logout, name="user_logout"),
    
    # Reset password urls
    path('password-reset', views.CustomPasswordResetView.as_view(), name='custom_password_reset'),
    path('password-reset/sent', views.CustomPasswordResetSentView.as_view(), name='custom_password_reset_sent'),
    path('password-reset/confirm/<uidb64>/<token>', views.CustomPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('password-reset/complete', views.CustomPasswordResetCompleteView.as_view(), name='custom_password_reset_complete'),
]
