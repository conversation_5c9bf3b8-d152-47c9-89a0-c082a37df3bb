# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class AddressesAddress(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    name = models.CharField(max_length=100, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.Char<PERSON>ield(max_length=100)
    phone_number = models.Char<PERSON><PERSON>(max_length=14)

    class Meta:
        managed = False
        db_table = 'addresses_address'


class AuthGroup(models.Model):
    name = models.CharField(unique=True, max_length=150)

    class Meta:
        managed = False
        db_table = 'auth_group'


class AuthGroupPermissions(models.Model):
    id = models.BigAutoField(primary_key=True)
    group = models.ForeignKey(AuthGroup, models.DO_NOTHING)
    permission = models.ForeignKey('AuthPermission', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'auth_group_permissions'
        unique_together = (('group', 'permission'),)


class AuthPermission(models.Model):
    name = models.CharField(max_length=255)
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING)
    codename = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'auth_permission'
        unique_together = (('content_type', 'codename'),)


class CompanyCompany(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    business_name = models.CharField(unique=True, max_length=100)
    rc_number = models.CharField(unique=True)
    company_address = models.TextField()
    cac_certificate = models.CharField(max_length=500, blank=True, null=True)
    user = models.ForeignKey('UsersCustomuser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'company_company'
        unique_together = (('user', 'business_name'),)


class DjangoAdminLog(models.Model):
    action_time = models.DateTimeField()
    object_id = models.TextField(blank=True, null=True)
    object_repr = models.CharField(max_length=200)
    action_flag = models.SmallIntegerField()
    change_message = models.TextField()
    content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING, blank=True, null=True)
    user = models.ForeignKey('UsersCustomuser', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'django_admin_log'


class DjangoContentType(models.Model):
    app_label = models.CharField(max_length=100)
    model = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'django_content_type'
        unique_together = (('app_label', 'model'),)


class DjangoMigrations(models.Model):
    id = models.BigAutoField(primary_key=True)
    app = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    applied = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_migrations'


class DjangoSession(models.Model):
    session_key = models.CharField(primary_key=True, max_length=40)
    session_data = models.TextField()
    expire_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_session'


class ExportsExport(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    phone_no = models.CharField(max_length=14)
    weight = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    lenght = models.FloatField()
    breadth = models.FloatField()
    height = models.FloatField()
    volumentary_weight = models.FloatField()
    export_fee = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    tracking_no = models.CharField(max_length=50)
    payment_status = models.CharField(max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(blank=True, null=True)
    tracking_url = models.CharField(max_length=200)
    shipping_status = models.CharField(max_length=255)
    attention = models.TextField()
    customer = models.ForeignKey('UsersCustomuser', models.DO_NOTHING)
    sender = models.ForeignKey(AddressesAddress, models.DO_NOTHING, blank=True, null=True)
    volumentary_weight_unit = models.CharField()
    weight_unit = models.CharField()
    email_sent = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'exports_export'


class ExportsItem(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    name = models.CharField(max_length=255)
    quantity = models.IntegerField()
    price_currency = models.CharField(max_length=3)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price_currency = models.CharField(max_length=3, blank=True, null=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    weight = models.FloatField()
    export = models.ForeignKey(ExportsExport, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'exports_item'


class GalleriesGallery(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    title = models.CharField(max_length=100)
    picture = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'galleries_gallery'


class ImportsImport(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    phone_no = models.CharField(max_length=14)
    weight = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    payment_status = models.CharField(max_length=100)
    tracking_no = models.CharField(max_length=50)
    shipped_at = models.DateTimeField(blank=True, null=True)
    attention = models.TextField()
    customer = models.ForeignKey('UsersCustomuser', models.DO_NOTHING)
    breadth = models.FloatField()
    height = models.FloatField()
    import_fee = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    lenght = models.FloatField()
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    sender = models.ForeignKey(AddressesAddress, models.DO_NOTHING, blank=True, null=True)
    shipping_status = models.CharField(max_length=255)
    volumentary_weight = models.FloatField()
    volumentary_weight_unit = models.CharField()
    weight_unit = models.CharField()
    email_sent = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'imports_import'


class ImportsItem(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    name = models.CharField(max_length=255)
    quantity = models.IntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    weight = models.FloatField()
    imports = models.ForeignKey(ImportsImport, models.DO_NOTHING, blank=True, null=True)
    price_currency = models.CharField(max_length=3)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    total_price_currency = models.CharField(max_length=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'imports_item'


class LogisticsItem(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    name = models.CharField(max_length=255)
    quantity = models.IntegerField()
    price_currency = models.CharField(max_length=3)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price_currency = models.CharField(max_length=3, blank=True, null=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    weight = models.FloatField()
    logistics = models.ForeignKey('LogisticsLogistic', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'logistics_item'


class LogisticsLogistic(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    receiver_name = models.CharField(max_length=255)
    receiver_address = models.CharField(max_length=100)
    receiver_city = models.CharField(max_length=100)
    receiver_state = models.CharField(max_length=100)
    phone_no = models.CharField(max_length=14)
    weight = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    weight_unit = models.CharField()
    lenght = models.FloatField()
    breadth = models.FloatField()
    height = models.FloatField()
    volumentary_weight = models.FloatField()
    volumentary_weight_unit = models.CharField()
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    tracking_no = models.CharField(max_length=50)
    payment_status = models.CharField(max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(blank=True, null=True)
    delivery_status = models.CharField(max_length=255)
    attention = models.TextField()
    email_sent = models.BooleanField()
    sender = models.ForeignKey(AddressesAddress, models.DO_NOTHING, blank=True, null=True)
    dispatcher = models.ForeignKey('UsersCustomuser', models.DO_NOTHING, blank=True, null=True)
    company = models.ForeignKey(CompanyCompany, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'logistics_logistic'


class PricingPricing(models.Model):
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    title = models.CharField(max_length=100)
    image = models.CharField(max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'pricing_pricing'


class UsersCustomuser(models.Model):
    password = models.CharField(max_length=128)
    last_login = models.DateTimeField(blank=True, null=True)
    id = models.CharField(primary_key=True, max_length=64)
    created_on = models.DateTimeField()
    updated_on = models.DateTimeField()
    profile_code = models.CharField(unique=True, max_length=100, blank=True, null=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.CharField(unique=True, max_length=100)
    gender = models.CharField(max_length=10)
    phone_no = models.CharField(max_length=14)
    address = models.CharField(max_length=500)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    document_type = models.CharField(max_length=100)
    document = models.CharField(max_length=500, blank=True, null=True)
    profile_picture = models.CharField(max_length=500, blank=True, null=True)
    is_active = models.BooleanField()
    is_staff = models.BooleanField()
    is_superuser = models.BooleanField()
    user_type = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'users_customuser'


class UsersCustomuserGroups(models.Model):
    id = models.BigAutoField(primary_key=True)
    customuser = models.ForeignKey(UsersCustomuser, models.DO_NOTHING)
    group = models.ForeignKey(AuthGroup, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'users_customuser_groups'
        unique_together = (('customuser', 'group'),)


class UsersCustomuserUserPermissions(models.Model):
    id = models.BigAutoField(primary_key=True)
    customuser = models.ForeignKey(UsersCustomuser, models.DO_NOTHING)
    permission = models.ForeignKey(AuthPermission, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'users_customuser_user_permissions'
        unique_together = (('customuser', 'permission'),)
