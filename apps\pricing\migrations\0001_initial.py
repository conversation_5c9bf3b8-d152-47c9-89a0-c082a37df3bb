# Generated by Django 4.2.14 on 2025-07-17 23:48

import apps.common.custom_validators
import cloudinary.models
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Pricing',
            fields=[
                ('id', models.CharField(db_index=True, default=uuid.uuid4, editable=False, max_length=64, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=100)),
                ('image', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, validators=[apps.common.custom_validators.custom_file_validator])),
            ],
            options={
                'verbose_name': 'Pricing',
                'verbose_name_plural': 'Pricing',
            },
        ),
    ]
