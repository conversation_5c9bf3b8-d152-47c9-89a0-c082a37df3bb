from rest_framework import serializers
from .models import Logistic, Item
from apps.addresses.models import Address
from apps.company.models import Company
from apps.users.models import CustomUser


class LogisticItemSerializer(serializers.ModelSerializer):
    """Serializer for Logistic Items"""
    
    class Meta:
        model = Item
        fields = ['id', 'name', 'quantity', 'price', 'total_price', 'weight']
        read_only_fields = ['id', 'total_price']


class LogisticCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Logistic orders"""
    items = LogisticItemSerializer(many=True, write_only=True)
    
    class Meta:
        model = Logistic
        fields = [
            'sender', 'receiver_name', 'receiver_address', 'receiver_city',
            'receiver_state', 'phone_no', 'weight', 'weight_unit',
            'lenght', 'breadth', 'height', 'attention', 'items'
        ]
    
    def validate(self, attrs):
        """Validate logistic data"""
        items = attrs.get('items', [])
        if not items:
            raise serializers.ValidationError("At least one item is required.")
        
        # Validate that sender exists
        sender_id = attrs.get('sender')
        if sender_id and not Address.objects.filter(id=sender_id.id).exists():
            raise serializers.ValidationError("Invalid sender address.")
        
        return attrs
    
    def create(self, validated_data):
        """Create logistic order with items"""
        items_data = validated_data.pop('items')
        user = self.context['request'].user
        
        # Get user's company
        try:
            company = Company.objects.get(user=user)
        except Company.DoesNotExist:
            raise serializers.ValidationError("User must have a registered company to create logistics orders.")
        
        # Create logistic order
        logistic_order = Logistic.objects.create(
            company=company,
            **validated_data
        )
        
        # Create items
        total_delivery_fee = 0
        for item_data in items_data:
            item = Item.objects.create(
                logistics=logistic_order,
                **item_data
            )
            total_delivery_fee += float(item.total_price.amount)
        
        # Calculate and set delivery fee (you can customize this logic)
        logistic_order.delivery_fee = total_delivery_fee * 0.15  # 15% of total item value
        logistic_order.save()
        
        return logistic_order


class LogisticListSerializer(serializers.ModelSerializer):
    """Serializer for listing Logistic orders"""
    sender = serializers.SerializerMethodField()
    receiver_address_display = serializers.SerializerMethodField()
    weight_display = serializers.SerializerMethodField()
    dispatcher = serializers.SerializerMethodField()
    
    class Meta:
        model = Logistic
        fields = [
            'id', 'tracking_no', 'sender', 'receiver_name', 'receiver_address_display',
            'delivery_fee', 'payment_status', 'delivery_status', 'dispatcher', 'created_on'
        ]
    
    def get_sender(self, obj):
        """Get sender address info"""
        if obj.sender:
            return {
                'name': obj.sender.name,
                'address': obj.sender.get_short_address()
            }
        return None
    
    def get_receiver_address_display(self, obj):
        """Get formatted receiver address"""
        return obj.get_receiver_address()
    
    def get_weight_display(self, obj):
        """Get formatted weight"""
        return obj.get_weight
    
    def get_dispatcher(self, obj):
        """Get dispatcher info"""
        if obj.dispatcher:
            return {
                'id': str(obj.dispatcher.id),
                'name': f"{obj.dispatcher.first_name} {obj.dispatcher.last_name}"
            }
        return None


class LogisticDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed Logistic order view"""
    company = serializers.SerializerMethodField()
    sender = serializers.SerializerMethodField()
    items = LogisticItemSerializer(source='item_set', many=True, read_only=True)
    dimensions = serializers.SerializerMethodField()
    dispatcher = serializers.SerializerMethodField()
    
    class Meta:
        model = Logistic
        fields = [
            'id', 'tracking_no', 'company', 'sender', 'receiver_name',
            'receiver_address', 'receiver_city', 'receiver_state', 'phone_no',
            'weight', 'weight_unit', 'dimensions', 'delivery_fee', 'payment_status',
            'payment_reference', 'delivery_status', 'shipped_at', 'attention',
            'dispatcher', 'items', 'created_on', 'updated_on'
        ]
    
    def get_company(self, obj):
        """Get company info"""
        return {
            'id': str(obj.company.id),
            'business_name': obj.company.business_name,
            'rc_number': obj.company.rc_number
        }
    
    def get_sender(self, obj):
        """Get detailed sender address"""
        if obj.sender:
            return {
                'id': str(obj.sender.id),
                'name': obj.sender.name,
                'address': obj.sender.address,
                'city': obj.sender.city,
                'state': obj.sender.state,
                'country': obj.sender.country,
                'phone_number': str(obj.sender.phone_number)
            }
        return None
    
    def get_dimensions(self, obj):
        """Get shipment dimensions"""
        return {
            'length': obj.lenght,
            'breadth': obj.breadth,
            'height': obj.height
        }
    
    def get_dispatcher(self, obj):
        """Get detailed dispatcher info"""
        if obj.dispatcher:
            return {
                'id': str(obj.dispatcher.id),
                'name': f"{obj.dispatcher.first_name} {obj.dispatcher.last_name}",
                'email': obj.dispatcher.email,
                'phone_no': str(obj.dispatcher.phone_no)
            }
        return None


class LogisticTrackingSerializer(serializers.ModelSerializer):
    """Serializer for public logistic tracking"""
    company = serializers.SerializerMethodField()
    sender = serializers.SerializerMethodField()
    receiver_address_display = serializers.SerializerMethodField()
    weight_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Logistic
        fields = [
            'tracking_no', 'company', 'sender', 'receiver_name', 'receiver_address_display',
            'weight_display', 'delivery_status', 'shipped_at', 'created_on'
        ]
    
    def get_company(self, obj):
        """Get company name for tracking"""
        return obj.company.business_name
    
    def get_sender(self, obj):
        """Get sender location for tracking"""
        if obj.sender:
            return {
                'name': obj.sender.name,
                'address': obj.sender.get_short_address()
            }
        return None
    
    def get_receiver_address_display(self, obj):
        """Get receiver address for tracking"""
        return obj.get_receiver_address()
    
    def get_weight_display(self, obj):
        """Get formatted weight for tracking"""
        return obj.get_weight


class LogisticStatusUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating delivery status (Dispatcher use)"""
    
    class Meta:
        model = Logistic
        fields = ['delivery_status']
    
    def validate_delivery_status(self, value):
        """Validate delivery status"""
        valid_statuses = ['Pending', 'In Transit', 'Delivered', 'Cancelled']
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
        return value
