from rest_framework import generics, permissions, status
from rest_framework.response import Response
from .models import Gallery
from .serializers import GallerySerializer, GalleryCreateSerializer


class GalleryListAPIView(generics.ListAPIView):
    """API View to list all gallery images"""
    queryset = Gallery.objects.all().order_by('-created_on')
    serializer_class = GallerySerializer
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def list(self, request, *args, **kwargs):
        """List all gallery images"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class GalleryDetailAPIView(generics.RetrieveAPIView):
    """API View to get specific gallery image"""
    queryset = Gallery.objects.all()
    serializer_class = GallerySerializer
    permission_classes = [permissions.AllowAny]  # Public endpoint
    lookup_field = 'id'
    
    def retrieve(self, request, *args, **kwargs):
        """Get specific gallery image"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Gallery image not found'
            }, status=status.HTTP_404_NOT_FOUND)


class GalleryCreateAPIView(generics.CreateAPIView):
    """API View to create new gallery image (Admin only)"""
    serializer_class = GalleryCreateSerializer
    permission_classes = [permissions.IsAdminUser]  # Admin only
    
    def create(self, request, *args, **kwargs):
        """Create new gallery image"""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            gallery_item = serializer.save()
            
            # Return created gallery data
            response_serializer = GallerySerializer(gallery_item)
            
            return Response({
                'success': True,
                'message': 'Gallery image created successfully',
                'data': response_serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Gallery image creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class GalleryUpdateAPIView(generics.UpdateAPIView):
    """API View to update gallery image (Admin only)"""
    queryset = Gallery.objects.all()
    serializer_class = GalleryCreateSerializer
    permission_classes = [permissions.IsAdminUser]  # Admin only
    lookup_field = 'id'
    
    def update(self, request, *args, **kwargs):
        """Update gallery image"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            
            if serializer.is_valid():
                serializer.save()
                
                # Return updated gallery data
                response_serializer = GallerySerializer(instance)
                
                return Response({
                    'success': True,
                    'message': 'Gallery image updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
            
            return Response({
                'success': False,
                'message': 'Gallery image update failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except:
            return Response({
                'success': False,
                'message': 'Gallery image not found'
            }, status=status.HTTP_404_NOT_FOUND)


class GalleryDeleteAPIView(generics.DestroyAPIView):
    """API View to delete gallery image (Admin only)"""
    queryset = Gallery.objects.all()
    permission_classes = [permissions.IsAdminUser]  # Admin only
    lookup_field = 'id'
    
    def destroy(self, request, *args, **kwargs):
        """Delete gallery image"""
        try:
            instance = self.get_object()
            instance.delete()
            
            return Response({
                'success': True,
                'message': 'Gallery image deleted successfully'
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Gallery image not found'
            }, status=status.HTTP_404_NOT_FOUND)
