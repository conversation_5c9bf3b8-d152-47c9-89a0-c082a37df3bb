/*!
 *  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@font-face {
  font-family: 'FontAwesome';
  src: url('../fonts/fontawesome-webfont.eot?v=4.5.0');
  src: url('../fonts/fontawesome-webfont.eot?#iefix&v=4.5.0') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff2?v=4.5.0') format('woff2'), url('../fonts/fontawesome-webfont.woff?v=4.5.0') format('woff'), url('../fonts/fontawesome-webfont.ttf?v=4.5.0') format('truetype'), url('../fonts/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^="fa-icon-"], [class*=" fa-icon-"] {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* makes the font 33% larger relative to the icon container */
.fa-icon-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}
.fa-icon-2x {
  font-size: 2em;
}
.fa-icon-3x {
  font-size: 3em;
}
.fa-icon-4x {
  font-size: 4em;
}
.fa-icon-5x {
  font-size: 5em;
}
.fa-icon-fw {
  width: 1.28571429em;
  text-align: center;
}
.fa-icon-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}
.fa-icon-ul > li {
  position: relative;
}
.fa-icon-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}
.fa-icon-li.fa-icon-lg {
  left: -1.85714286em;
}
.fa-icon-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eeeeee;
  border-radius: .1em;
}
.fa-icon-pull-left {
  float: left;
}
.fa-icon-pull-right {
  float: right;
}
.fa-icon.fa-icon-pull-left {
  margin-right: .3em;
}
.fa-icon.fa-icon-pull-right {
  margin-left: .3em;
}
/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.fa-icon.pull-left {
  margin-right: .3em;
}
.fa-icon.pull-right {
  margin-left: .3em;
}
.fa-icon-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}
.fa-icon-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}
@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
.fa-icon-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.fa-icon-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.fa-icon-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}
.fa-icon-flip-horizontal {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}
.fa-icon-flip-vertical {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}
:root .fa-icon-rotate-90,
:root .fa-icon-rotate-180,
:root .fa-icon-rotate-270,
:root .fa-icon-flip-horizontal,
:root .fa-icon-flip-vertical {
  filter: none;
}
.fa-icon-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.fa-icon-stack-1x,
.fa-icon-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}
.fa-icon-stack-1x {
  line-height: inherit;
}
.fa-icon-stack-2x {
  font-size: 2em;
}
.fa-icon-inverse {
  color: #ffffff;
}
/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-icon-glass:before {
  content: "\f000";
}
.fa-icon-music:before {
  content: "\f001";
}
.fa-icon-search:before {
  content: "\f002";
}
.fa-icon-envelope-o:before {
  content: "\f003";
}
.fa-icon-heart:before {
  content: "\f004";
}
.fa-icon-star:before {
  content: "\f005";
}
.fa-icon-star-o:before {
  content: "\f006";
}
.fa-icon-user:before {
  content: "\f007";
}
.fa-icon-film:before {
  content: "\f008";
}
.fa-icon-th-large:before {
  content: "\f009";
}
.fa-icon-th:before {
  content: "\f00a";
}
.fa-icon-th-list:before {
  content: "\f00b";
}
.fa-icon-check:before {
  content: "\f00c";
}
.fa-icon-remove:before,
.fa-icon-close:before,
.fa-icon-times:before {
  content: "\f00d";
}
.fa-icon-search-plus:before {
  content: "\f00e";
}
.fa-icon-search-minus:before {
  content: "\f010";
}
.fa-icon-power-off:before {
  content: "\f011";
}
.fa-icon-signal:before {
  content: "\f012";
}
.fa-icon-gear:before,
.fa-icon-cog:before {
  content: "\f013";
}
.fa-icon-trash-o:before {
  content: "\f014";
}
.fa-icon-home:before {
  content: "\f015";
}
.fa-icon-file-o:before {
  content: "\f016";
}
.fa-icon-clock-o:before {
  content: "\f017";
}
.fa-icon-road:before {
  content: "\f018";
}
.fa-icon-download:before {
  content: "\f019";
}
.fa-icon-arrow-circle-o-down:before {
  content: "\f01a";
}
.fa-icon-arrow-circle-o-up:before {
  content: "\f01b";
}
.fa-icon-inbox:before {
  content: "\f01c";
}
.fa-icon-play-circle-o:before {
  content: "\f01d";
}
.fa-icon-rotate-right:before,
.fa-icon-repeat:before {
  content: "\f01e";
}
.fa-icon-refresh:before {
  content: "\f021";
}
.fa-icon-list-alt:before {
  content: "\f022";
}
.fa-icon-lock:before {
  content: "\f023";
}
.fa-icon-flag:before {
  content: "\f024";
}
.fa-icon-headphones:before {
  content: "\f025";
}
.fa-icon-volume-off:before {
  content: "\f026";
}
.fa-icon-volume-down:before {
  content: "\f027";
}
.fa-icon-volume-up:before {
  content: "\f028";
}
.fa-icon-qrcode:before {
  content: "\f029";
}
.fa-icon-barcode:before {
  content: "\f02a";
}
.fa-icon-tag:before {
  content: "\f02b";
}
.fa-icon-tags:before {
  content: "\f02c";
}
.fa-icon-book:before {
  content: "\f02d";
}
.fa-icon-bookmark:before {
  content: "\f02e";
}
.fa-icon-print:before {
  content: "\f02f";
}
.fa-icon-camera:before {
  content: "\f030";
}
.fa-icon-font:before {
  content: "\f031";
}
.fa-icon-bold:before {
  content: "\f032";
}
.fa-icon-italic:before {
  content: "\f033";
}
.fa-icon-text-height:before {
  content: "\f034";
}
.fa-icon-text-width:before {
  content: "\f035";
}
.fa-icon-align-left:before {
  content: "\f036";
}
.fa-icon-align-center:before {
  content: "\f037";
}
.fa-icon-align-right:before {
  content: "\f038";
}
.fa-icon-align-justify:before {
  content: "\f039";
}
.fa-icon-list:before {
  content: "\f03a";
}
.fa-icon-dedent:before,
.fa-icon-outdent:before {
  content: "\f03b";
}
.fa-icon-indent:before {
  content: "\f03c";
}
.fa-icon-video-camera:before {
  content: "\f03d";
}
.fa-icon-photo:before,
.fa-icon-image:before,
.fa-icon-picture-o:before {
  content: "\f03e";
}
.fa-icon-pencil:before {
  content: "\f040";
}
.fa-icon-map-marker:before {
  content: "\f041";
}
.fa-icon-adjust:before {
  content: "\f042";
}
.fa-icon-tint:before {
  content: "\f043";
}
.fa-icon-edit:before,
.fa-icon-pencil-square-o:before {
  content: "\f044";
}
.fa-icon-share-square-o:before {
  content: "\f045";
}
.fa-icon-check-square-o:before {
  content: "\f046";
}
.fa-icon-arrows:before {
  content: "\f047";
}
.fa-icon-step-backward:before {
  content: "\f048";
}
.fa-icon-fast-backward:before {
  content: "\f049";
}
.fa-icon-backward:before {
  content: "\f04a";
}
.fa-icon-play:before {
  content: "\f04b";
}
.fa-icon-pause:before {
  content: "\f04c";
}
.fa-icon-stop:before {
  content: "\f04d";
}
.fa-icon-forward:before {
  content: "\f04e";
}
.fa-icon-fast-forward:before {
  content: "\f050";
}
.fa-icon-step-forward:before {
  content: "\f051";
}
.fa-icon-eject:before {
  content: "\f052";
}
.fa-icon-chevron-left:before {
  content: "\f053";
}
.fa-icon-chevron-right:before {
  content: "\f054";
}
.fa-icon-plus-circle:before {
  content: "\f055";
}
.fa-icon-minus-circle:before {
  content: "\f056";
}
.fa-icon-times-circle:before {
  content: "\f057";
}
.fa-icon-check-circle:before {
  content: "\f058";
}
.fa-icon-question-circle:before {
  content: "\f059";
}
.fa-icon-info-circle:before {
  content: "\f05a";
}
.fa-icon-crosshairs:before {
  content: "\f05b";
}
.fa-icon-times-circle-o:before {
  content: "\f05c";
}
.fa-icon-check-circle-o:before {
  content: "\f05d";
}
.fa-icon-ban:before {
  content: "\f05e";
}
.fa-icon-arrow-left:before {
  content: "\f060";
}
.fa-icon-arrow-right:before {
  content: "\f061";
}
.fa-icon-arrow-up:before {
  content: "\f062";
}
.fa-icon-arrow-down:before {
  content: "\f063";
}
.fa-icon-mail-forward:before,
.fa-icon-share:before {
  content: "\f064";
}
.fa-icon-expand:before {
  content: "\f065";
}
.fa-icon-compress:before {
  content: "\f066";
}
.fa-icon-plus:before {
  content: "\f067";
}
.fa-icon-minus:before {
  content: "\f068";
}
.fa-icon-asterisk:before {
  content: "\f069";
}
.fa-icon-exclamation-circle:before {
  content: "\f06a";
}
.fa-icon-gift:before {
  content: "\f06b";
}
.fa-icon-leaf:before {
  content: "\f06c";
}
.fa-icon-fire:before {
  content: "\f06d";
}
.fa-icon-eye:before {
  content: "\f06e";
}
.fa-icon-eye-slash:before {
  content: "\f070";
}
.fa-icon-warning:before,
.fa-icon-exclamation-triangle:before {
  content: "\f071";
}
.fa-icon-plane:before {
  content: "\f072";
}
.fa-icon-calendar:before {
  content: "\f073";
}
.fa-icon-random:before {
  content: "\f074";
}
.fa-icon-comment:before {
  content: "\f075";
}
.fa-icon-magnet:before {
  content: "\f076";
}
.fa-icon-chevron-up:before {
  content: "\f077";
}
.fa-icon-chevron-down:before {
  content: "\f078";
}
.fa-icon-retweet:before {
  content: "\f079";
}
.fa-icon-shopping-cart:before {
  content: "\f07a";
}
.fa-icon-folder:before {
  content: "\f07b";
}
.fa-icon-folder-open:before {
  content: "\f07c";
}
.fa-icon-arrows-v:before {
  content: "\f07d";
}
.fa-icon-arrows-h:before {
  content: "\f07e";
}
.fa-icon-bar-chart-o:before,
.fa-icon-bar-chart:before {
  content: "\f080";
}
.fa-icon-twitter-square:before {
  content: "\f081";
}
.fa-icon-facebook-square:before {
  content: "\f082";
}
.fa-icon-camera-retro:before {
  content: "\f083";
}
.fa-icon-key:before {
  content: "\f084";
}
.fa-icon-gears:before,
.fa-icon-cogs:before {
  content: "\f085";
}
.fa-icon-comments:before {
  content: "\f086";
}
.fa-icon-thumbs-o-up:before {
  content: "\f087";
}
.fa-icon-thumbs-o-down:before {
  content: "\f088";
}
.fa-icon-star-half:before {
  content: "\f089";
}
.fa-icon-heart-o:before {
  content: "\f08a";
}
.fa-icon-sign-out:before {
  content: "\f08b";
}
.fa-icon-linkedin-square:before {
  content: "\f08c";
}
.fa-icon-thumb-tack:before {
  content: "\f08d";
}
.fa-icon-external-link:before {
  content: "\f08e";
}
.fa-icon-sign-in:before {
  content: "\f090";
}
.fa-icon-trophy:before {
  content: "\f091";
}
.fa-icon-github-square:before {
  content: "\f092";
}
.fa-icon-upload:before {
  content: "\f093";
}
.fa-icon-lemon-o:before {
  content: "\f094";
}
.fa-icon-phone:before {
  content: "\f095";
}
.fa-icon-square-o:before {
  content: "\f096";
}
.fa-icon-bookmark-o:before {
  content: "\f097";
}
.fa-icon-phone-square:before {
  content: "\f098";
}
.fa-icon-twitter:before {
  content: "\f099";
}
.fa-icon-facebook-f:before,
.fa-icon-facebook:before {
  content: "\f09a";
}
.fa-icon-github:before {
  content: "\f09b";
}
.fa-icon-unlock:before {
  content: "\f09c";
}
.fa-icon-credit-card:before {
  content: "\f09d";
}
.fa-icon-feed:before,
.fa-icon-rss:before {
  content: "\f09e";
}
.fa-icon-hdd-o:before {
  content: "\f0a0";
}
.fa-icon-bullhorn:before {
  content: "\f0a1";
}
.fa-icon-bell:before {
  content: "\f0f3";
}
.fa-icon-certificate:before {
  content: "\f0a3";
}
.fa-icon-hand-o-right:before {
  content: "\f0a4";
}
.fa-icon-hand-o-left:before {
  content: "\f0a5";
}
.fa-icon-hand-o-up:before {
  content: "\f0a6";
}
.fa-icon-hand-o-down:before {
  content: "\f0a7";
}
.fa-icon-arrow-circle-left:before {
  content: "\f0a8";
}
.fa-icon-arrow-circle-right:before {
  content: "\f0a9";
}
.fa-icon-arrow-circle-up:before {
  content: "\f0aa";
}
.fa-icon-arrow-circle-down:before {
  content: "\f0ab";
}
.fa-icon-globe:before {
  content: "\f0ac";
}
.fa-icon-wrench:before {
  content: "\f0ad";
}
.fa-icon-tasks:before {
  content: "\f0ae";
}
.fa-icon-filter:before {
  content: "\f0b0";
}
.fa-icon-briefcase:before {
  content: "\f0b1";
}
.fa-icon-arrows-alt:before {
  content: "\f0b2";
}
.fa-icon-group:before,
.fa-icon-users:before {
  content: "\f0c0";
}
.fa-icon-chain:before,
.fa-icon-link:before {
  content: "\f0c1";
}
.fa-icon-cloud:before {
  content: "\f0c2";
}
.fa-icon-flask:before {
  content: "\f0c3";
}
.fa-icon-cut:before,
.fa-icon-scissors:before {
  content: "\f0c4";
}
.fa-icon-copy:before,
.fa-icon-files-o:before {
  content: "\f0c5";
}
.fa-icon-paperclip:before {
  content: "\f0c6";
}
.fa-icon-save:before,
.fa-icon-floppy-o:before {
  content: "\f0c7";
}
.fa-icon-square:before {
  content: "\f0c8";
}
.fa-icon-navicon:before,
.fa-icon-reorder:before,
.fa-icon-bars:before {
  content: "\f0c9";
}
.fa-icon-list-ul:before {
  content: "\f0ca";
}
.fa-icon-list-ol:before {
  content: "\f0cb";
}
.fa-icon-strikethrough:before {
  content: "\f0cc";
}
.fa-icon-underline:before {
  content: "\f0cd";
}
.fa-icon-table:before {
  content: "\f0ce";
}
.fa-icon-magic:before {
  content: "\f0d0";
}
.fa-icon-truck:before {
  content: "\f0d1";
}
.fa-icon-pinterest:before {
  content: "\f0d2";
}
.fa-icon-pinterest-square:before {
  content: "\f0d3";
}
.fa-icon-google-plus-square:before {
  content: "\f0d4";
}
.fa-icon-google-plus:before {
  content: "\f0d5";
}
.fa-icon-money:before {
  content: "\f0d6";
}
.fa-icon-caret-down:before {
  content: "\f0d7";
}
.fa-icon-caret-up:before {
  content: "\f0d8";
}
.fa-icon-caret-left:before {
  content: "\f0d9";
}
.fa-icon-caret-right:before {
  content: "\f0da";
}
.fa-icon-columns:before {
  content: "\f0db";
}
.fa-icon-unsorted:before,
.fa-icon-sort:before {
  content: "\f0dc";
}
.fa-icon-sort-down:before,
.fa-icon-sort-desc:before {
  content: "\f0dd";
}
.fa-icon-sort-up:before,
.fa-icon-sort-asc:before {
  content: "\f0de";
}
.fa-icon-envelope:before {
  content: "\f0e0";
}
.fa-icon-linkedin:before {
  content: "\f0e1";
}
.fa-icon-rotate-left:before,
.fa-icon-undo:before {
  content: "\f0e2";
}
.fa-icon-legal:before,
.fa-icon-gavel:before {
  content: "\f0e3";
}
.fa-icon-dashboard:before,
.fa-icon-tachometer:before {
  content: "\f0e4";
}
.fa-icon-comment-o:before {
  content: "\f0e5";
}
.fa-icon-comments-o:before {
  content: "\f0e6";
}
.fa-icon-flash:before,
.fa-icon-bolt:before {
  content: "\f0e7";
}
.fa-icon-sitemap:before {
  content: "\f0e8";
}
.fa-icon-umbrella:before {
  content: "\f0e9";
}
.fa-icon-paste:before,
.fa-icon-clipboard:before {
  content: "\f0ea";
}
.fa-icon-lightbulb-o:before {
  content: "\f0eb";
}
.fa-icon-exchange:before {
  content: "\f0ec";
}
.fa-icon-cloud-download:before {
  content: "\f0ed";
}
.fa-icon-cloud-upload:before {
  content: "\f0ee";
}
.fa-icon-user-md:before {
  content: "\f0f0";
}
.fa-icon-stethoscope:before {
  content: "\f0f1";
}
.fa-icon-suitcase:before {
  content: "\f0f2";
}
.fa-icon-bell-o:before {
  content: "\f0a2";
}
.fa-icon-coffee:before {
  content: "\f0f4";
}
.fa-icon-cutlery:before {
  content: "\f0f5";
}
.fa-icon-file-text-o:before {
  content: "\f0f6";
}
.fa-icon-building-o:before {
  content: "\f0f7";
}
.fa-icon-hospital-o:before {
  content: "\f0f8";
}
.fa-icon-ambulance:before {
  content: "\f0f9";
}
.fa-icon-medkit:before {
  content: "\f0fa";
}
.fa-icon-fighter-jet:before {
  content: "\f0fb";
}
.fa-icon-beer:before {
  content: "\f0fc";
}
.fa-icon-h-square:before {
  content: "\f0fd";
}
.fa-icon-plus-square:before {
  content: "\f0fe";
}
.fa-icon-angle-double-left:before {
  content: "\f100";
}
.fa-icon-angle-double-right:before {
  content: "\f101";
}
.fa-icon-angle-double-up:before {
  content: "\f102";
}
.fa-icon-angle-double-down:before {
  content: "\f103";
}
.fa-icon-angle-left:before {
  content: "\f104";
}
.fa-icon-angle-right:before {
  content: "\f105";
}
.fa-icon-angle-up:before {
  content: "\f106";
}
.fa-icon-angle-down:before {
  content: "\f107";
}
.fa-icon-desktop:before {
  content: "\f108";
}
.fa-icon-laptop:before {
  content: "\f109";
}
.fa-icon-tablet:before {
  content: "\f10a";
}
.fa-icon-mobile-phone:before,
.fa-icon-mobile:before {
  content: "\f10b";
}
.fa-icon-circle-o:before {
  content: "\f10c";
}
.fa-icon-quote-left:before {
  content: "\f10d";
}
.fa-icon-quote-right:before {
  content: "\f10e";
}
.fa-icon-spinner:before {
  content: "\f110";
}
.fa-icon-circle:before {
  content: "\f111";
}
.fa-icon-mail-reply:before,
.fa-icon-reply:before {
  content: "\f112";
}
.fa-icon-github-alt:before {
  content: "\f113";
}
.fa-icon-folder-o:before {
  content: "\f114";
}
.fa-icon-folder-open-o:before {
  content: "\f115";
}
.fa-icon-smile-o:before {
  content: "\f118";
}
.fa-icon-frown-o:before {
  content: "\f119";
}
.fa-icon-meh-o:before {
  content: "\f11a";
}
.fa-icon-gamepad:before {
  content: "\f11b";
}
.fa-icon-keyboard-o:before {
  content: "\f11c";
}
.fa-icon-flag-o:before {
  content: "\f11d";
}
.fa-icon-flag-checkered:before {
  content: "\f11e";
}
.fa-icon-terminal:before {
  content: "\f120";
}
.fa-icon-code:before {
  content: "\f121";
}
.fa-icon-mail-reply-all:before,
.fa-icon-reply-all:before {
  content: "\f122";
}
.fa-icon-star-half-empty:before,
.fa-icon-star-half-full:before,
.fa-icon-star-half-o:before {
  content: "\f123";
}
.fa-icon-location-arrow:before {
  content: "\f124";
}
.fa-icon-crop:before {
  content: "\f125";
}
.fa-icon-code-fork:before {
  content: "\f126";
}
.fa-icon-unlink:before,
.fa-icon-chain-broken:before {
  content: "\f127";
}
.fa-icon-question:before {
  content: "\f128";
}
.fa-icon-info:before {
  content: "\f129";
}
.fa-icon-exclamation:before {
  content: "\f12a";
}
.fa-icon-superscript:before {
  content: "\f12b";
}
.fa-icon-subscript:before {
  content: "\f12c";
}
.fa-icon-eraser:before {
  content: "\f12d";
}
.fa-icon-puzzle-piece:before {
  content: "\f12e";
}
.fa-icon-microphone:before {
  content: "\f130";
}
.fa-icon-microphone-slash:before {
  content: "\f131";
}
.fa-icon-shield:before {
  content: "\f132";
}
.fa-icon-calendar-o:before {
  content: "\f133";
}
.fa-icon-fire-extinguisher:before {
  content: "\f134";
}
.fa-icon-rocket:before {
  content: "\f135";
}
.fa-icon-maxcdn:before {
  content: "\f136";
}
.fa-icon-chevron-circle-left:before {
  content: "\f137";
}
.fa-icon-chevron-circle-right:before {
  content: "\f138";
}
.fa-icon-chevron-circle-up:before {
  content: "\f139";
}
.fa-icon-chevron-circle-down:before {
  content: "\f13a";
}
.fa-icon-html5:before {
  content: "\f13b";
}
.fa-icon-css3:before {
  content: "\f13c";
}
.fa-icon-anchor:before {
  content: "\f13d";
}
.fa-icon-unlock-alt:before {
  content: "\f13e";
}
.fa-icon-bullseye:before {
  content: "\f140";
}
.fa-icon-ellipsis-h:before {
  content: "\f141";
}
.fa-icon-ellipsis-v:before {
  content: "\f142";
}
.fa-icon-rss-square:before {
  content: "\f143";
}
.fa-icon-play-circle:before {
  content: "\f144";
}
.fa-icon-ticket:before {
  content: "\f145";
}
.fa-icon-minus-square:before {
  content: "\f146";
}
.fa-icon-minus-square-o:before {
  content: "\f147";
}
.fa-icon-level-up:before {
  content: "\f148";
}
.fa-icon-level-down:before {
  content: "\f149";
}
.fa-icon-check-square:before {
  content: "\f14a";
}
.fa-icon-pencil-square:before {
  content: "\f14b";
}
.fa-icon-external-link-square:before {
  content: "\f14c";
}
.fa-icon-share-square:before {
  content: "\f14d";
}
.fa-icon-compass:before {
  content: "\f14e";
}
.fa-icon-toggle-down:before,
.fa-icon-caret-square-o-down:before {
  content: "\f150";
}
.fa-icon-toggle-up:before,
.fa-icon-caret-square-o-up:before {
  content: "\f151";
}
.fa-icon-toggle-right:before,
.fa-icon-caret-square-o-right:before {
  content: "\f152";
}
.fa-icon-euro:before,
.fa-icon-eur:before {
  content: "\f153";
}
.fa-icon-gbp:before {
  content: "\f154";
}
.fa-icon-dollar:before,
.fa-icon-usd:before {
  content: "\f155";
}
.fa-icon-rupee:before,
.fa-icon-inr:before {
  content: "\f156";
}
.fa-icon-cny:before,
.fa-icon-rmb:before,
.fa-icon-yen:before,
.fa-icon-jpy:before {
  content: "\f157";
}
.fa-icon-ruble:before,
.fa-icon-rouble:before,
.fa-icon-rub:before {
  content: "\f158";
}
.fa-icon-won:before,
.fa-icon-krw:before {
  content: "\f159";
}
.fa-icon-bitcoin:before,
.fa-icon-btc:before {
  content: "\f15a";
}
.fa-icon-file:before {
  content: "\f15b";
}
.fa-icon-file-text:before {
  content: "\f15c";
}
.fa-icon-sort-alpha-asc:before {
  content: "\f15d";
}
.fa-icon-sort-alpha-desc:before {
  content: "\f15e";
}
.fa-icon-sort-amount-asc:before {
  content: "\f160";
}
.fa-icon-sort-amount-desc:before {
  content: "\f161";
}
.fa-icon-sort-numeric-asc:before {
  content: "\f162";
}
.fa-icon-sort-numeric-desc:before {
  content: "\f163";
}
.fa-icon-thumbs-up:before {
  content: "\f164";
}
.fa-icon-thumbs-down:before {
  content: "\f165";
}
.fa-icon-youtube-square:before {
  content: "\f166";
}
.fa-icon-youtube:before {
  content: "\f167";
}
.fa-icon-xing:before {
  content: "\f168";
}
.fa-icon-xing-square:before {
  content: "\f169";
}
.fa-icon-youtube-play:before {
  content: "\f16a";
}
.fa-icon-dropbox:before {
  content: "\f16b";
}
.fa-icon-stack-overflow:before {
  content: "\f16c";
}
.fa-icon-instagram:before {
  content: "\f16d";
}
.fa-icon-flickr:before {
  content: "\f16e";
}
.fa-icon-adn:before {
  content: "\f170";
}
.fa-icon-bitbucket:before {
  content: "\f171";
}
.fa-icon-bitbucket-square:before {
  content: "\f172";
}
.fa-icon-tumblr:before {
  content: "\f173";
}
.fa-icon-tumblr-square:before {
  content: "\f174";
}
.fa-icon-long-arrow-down:before {
  content: "\f175";
}
.fa-icon-long-arrow-up:before {
  content: "\f176";
}
.fa-icon-long-arrow-left:before {
  content: "\f177";
}
.fa-icon-long-arrow-right:before {
  content: "\f178";
}
.fa-icon-apple:before {
  content: "\f179";
}
.fa-icon-windows:before {
  content: "\f17a";
}
.fa-icon-android:before {
  content: "\f17b";
}
.fa-icon-linux:before {
  content: "\f17c";
}
.fa-icon-dribbble:before {
  content: "\f17d";
}
.fa-icon-skype:before {
  content: "\f17e";
}
.fa-icon-foursquare:before {
  content: "\f180";
}
.fa-icon-trello:before {
  content: "\f181";
}
.fa-icon-female:before {
  content: "\f182";
}
.fa-icon-male:before {
  content: "\f183";
}
.fa-icon-gittip:before,
.fa-icon-gratipay:before {
  content: "\f184";
}
.fa-icon-sun-o:before {
  content: "\f185";
}
.fa-icon-moon-o:before {
  content: "\f186";
}
.fa-icon-archive:before {
  content: "\f187";
}
.fa-icon-bug:before {
  content: "\f188";
}
.fa-icon-vk:before {
  content: "\f189";
}
.fa-icon-weibo:before {
  content: "\f18a";
}
.fa-icon-renren:before {
  content: "\f18b";
}
.fa-icon-pagelines:before {
  content: "\f18c";
}
.fa-icon-stack-exchange:before {
  content: "\f18d";
}
.fa-icon-arrow-circle-o-right:before {
  content: "\f18e";
}
.fa-icon-arrow-circle-o-left:before {
  content: "\f190";
}
.fa-icon-toggle-left:before,
.fa-icon-caret-square-o-left:before {
  content: "\f191";
}
.fa-icon-dot-circle-o:before {
  content: "\f192";
}
.fa-icon-wheelchair:before {
  content: "\f193";
}
.fa-icon-vimeo-square:before {
  content: "\f194";
}
.fa-icon-turkish-lira:before,
.fa-icon-try:before {
  content: "\f195";
}
.fa-icon-plus-square-o:before {
  content: "\f196";
}
.fa-icon-space-shuttle:before {
  content: "\f197";
}
.fa-icon-slack:before {
  content: "\f198";
}
.fa-icon-envelope-square:before {
  content: "\f199";
}
.fa-icon-wordpress:before {
  content: "\f19a";
}
.fa-icon-openid:before {
  content: "\f19b";
}
.fa-icon-institution:before,
.fa-icon-bank:before,
.fa-icon-university:before {
  content: "\f19c";
}
.fa-icon-mortar-board:before,
.fa-icon-graduation-cap:before {
  content: "\f19d";
}
.fa-icon-yahoo:before {
  content: "\f19e";
}
.fa-icon-google:before {
  content: "\f1a0";
}
.fa-icon-reddit:before {
  content: "\f1a1";
}
.fa-icon-reddit-square:before {
  content: "\f1a2";
}
.fa-icon-stumbleupon-circle:before {
  content: "\f1a3";
}
.fa-icon-stumbleupon:before {
  content: "\f1a4";
}
.fa-icon-delicious:before {
  content: "\f1a5";
}
.fa-icon-digg:before {
  content: "\f1a6";
}
.fa-icon-pied-piper:before {
  content: "\f1a7";
}
.fa-icon-pied-piper-alt:before {
  content: "\f1a8";
}
.fa-icon-drupal:before {
  content: "\f1a9";
}
.fa-icon-joomla:before {
  content: "\f1aa";
}
.fa-icon-language:before {
  content: "\f1ab";
}
.fa-icon-fax:before {
  content: "\f1ac";
}
.fa-icon-building:before {
  content: "\f1ad";
}
.fa-icon-child:before {
  content: "\f1ae";
}
.fa-icon-paw:before {
  content: "\f1b0";
}
.fa-icon-spoon:before {
  content: "\f1b1";
}
.fa-icon-cube:before {
  content: "\f1b2";
}
.fa-icon-cubes:before {
  content: "\f1b3";
}
.fa-icon-behance:before {
  content: "\f1b4";
}
.fa-icon-behance-square:before {
  content: "\f1b5";
}
.fa-icon-steam:before {
  content: "\f1b6";
}
.fa-icon-steam-square:before {
  content: "\f1b7";
}
.fa-icon-recycle:before {
  content: "\f1b8";
}
.fa-icon-automobile:before,
.fa-icon-car:before {
  content: "\f1b9";
}
.fa-icon-cab:before,
.fa-icon-taxi:before {
  content: "\f1ba";
}
.fa-icon-tree:before {
  content: "\f1bb";
}
.fa-icon-spotify:before {
  content: "\f1bc";
}
.fa-icon-deviantart:before {
  content: "\f1bd";
}
.fa-icon-soundcloud:before {
  content: "\f1be";
}
.fa-icon-database:before {
  content: "\f1c0";
}
.fa-icon-file-pdf-o:before {
  content: "\f1c1";
}
.fa-icon-file-word-o:before {
  content: "\f1c2";
}
.fa-icon-file-excel-o:before {
  content: "\f1c3";
}
.fa-icon-file-powerpoint-o:before {
  content: "\f1c4";
}
.fa-icon-file-photo-o:before,
.fa-icon-file-picture-o:before,
.fa-icon-file-image-o:before {
  content: "\f1c5";
}
.fa-icon-file-zip-o:before,
.fa-icon-file-archive-o:before {
  content: "\f1c6";
}
.fa-icon-file-sound-o:before,
.fa-icon-file-audio-o:before {
  content: "\f1c7";
}
.fa-icon-file-movie-o:before,
.fa-icon-file-video-o:before {
  content: "\f1c8";
}
.fa-icon-file-code-o:before {
  content: "\f1c9";
}
.fa-icon-vine:before {
  content: "\f1ca";
}
.fa-icon-codepen:before {
  content: "\f1cb";
}
.fa-icon-jsfiddle:before {
  content: "\f1cc";
}
.fa-icon-life-bouy:before,
.fa-icon-life-buoy:before,
.fa-icon-life-saver:before,
.fa-icon-support:before,
.fa-icon-life-ring:before {
  content: "\f1cd";
}
.fa-icon-circle-o-notch:before {
  content: "\f1ce";
}
.fa-icon-ra:before,
.fa-icon-rebel:before {
  content: "\f1d0";
}
.fa-icon-ge:before,
.fa-icon-empire:before {
  content: "\f1d1";
}
.fa-icon-git-square:before {
  content: "\f1d2";
}
.fa-icon-git:before {
  content: "\f1d3";
}
.fa-icon-y-combinator-square:before,
.fa-icon-yc-square:before,
.fa-icon-hacker-news:before {
  content: "\f1d4";
}
.fa-icon-tencent-weibo:before {
  content: "\f1d5";
}
.fa-icon-qq:before {
  content: "\f1d6";
}
.fa-icon-wechat:before,
.fa-icon-weixin:before {
  content: "\f1d7";
}
.fa-icon-send:before,
.fa-icon-paper-plane:before {
  content: "\f1d8";
}
.fa-icon-send-o:before,
.fa-icon-paper-plane-o:before {
  content: "\f1d9";
}
.fa-icon-history:before {
  content: "\f1da";
}
.fa-icon-circle-thin:before {
  content: "\f1db";
}
.fa-icon-header:before {
  content: "\f1dc";
}
.fa-icon-paragraph:before {
  content: "\f1dd";
}
.fa-icon-sliders:before {
  content: "\f1de";
}
.fa-icon-share-alt:before {
  content: "\f1e0";
}
.fa-icon-share-alt-square:before {
  content: "\f1e1";
}
.fa-icon-bomb:before {
  content: "\f1e2";
}
.fa-icon-soccer-ball-o:before,
.fa-icon-futbol-o:before {
  content: "\f1e3";
}
.fa-icon-tty:before {
  content: "\f1e4";
}
.fa-icon-binoculars:before {
  content: "\f1e5";
}
.fa-icon-plug:before {
  content: "\f1e6";
}
.fa-icon-slideshare:before {
  content: "\f1e7";
}
.fa-icon-twitch:before {
  content: "\f1e8";
}
.fa-icon-yelp:before {
  content: "\f1e9";
}
.fa-icon-newspaper-o:before {
  content: "\f1ea";
}
.fa-icon-wifi:before {
  content: "\f1eb";
}
.fa-icon-calculator:before {
  content: "\f1ec";
}
.fa-icon-paypal:before {
  content: "\f1ed";
}
.fa-icon-google-wallet:before {
  content: "\f1ee";
}
.fa-icon-cc-visa:before {
  content: "\f1f0";
}
.fa-icon-cc-mastercard:before {
  content: "\f1f1";
}
.fa-icon-cc-discover:before {
  content: "\f1f2";
}
.fa-icon-cc-amex:before {
  content: "\f1f3";
}
.fa-icon-cc-paypal:before {
  content: "\f1f4";
}
.fa-icon-cc-stripe:before {
  content: "\f1f5";
}
.fa-icon-bell-slash:before {
  content: "\f1f6";
}
.fa-icon-bell-slash-o:before {
  content: "\f1f7";
}
.fa-icon-trash:before {
  content: "\f1f8";
}
.fa-icon-copyright:before {
  content: "\f1f9";
}
.fa-icon-at:before {
  content: "\f1fa";
}
.fa-icon-eyedropper:before {
  content: "\f1fb";
}
.fa-icon-paint-brush:before {
  content: "\f1fc";
}
.fa-icon-birthday-cake:before {
  content: "\f1fd";
}
.fa-icon-area-chart:before {
  content: "\f1fe";
}
.fa-icon-pie-chart:before {
  content: "\f200";
}
.fa-icon-line-chart:before {
  content: "\f201";
}
.fa-icon-lastfm:before {
  content: "\f202";
}
.fa-icon-lastfm-square:before {
  content: "\f203";
}
.fa-icon-toggle-off:before {
  content: "\f204";
}
.fa-icon-toggle-on:before {
  content: "\f205";
}
.fa-icon-bicycle:before {
  content: "\f206";
}
.fa-icon-bus:before {
  content: "\f207";
}
.fa-icon-ioxhost:before {
  content: "\f208";
}
.fa-icon-angellist:before {
  content: "\f209";
}
.fa-icon-cc:before {
  content: "\f20a";
}
.fa-icon-shekel:before,
.fa-icon-sheqel:before,
.fa-icon-ils:before {
  content: "\f20b";
}
.fa-icon-meanpath:before {
  content: "\f20c";
}
.fa-icon-buysellads:before {
  content: "\f20d";
}
.fa-icon-connectdevelop:before {
  content: "\f20e";
}
.fa-icon-dashcube:before {
  content: "\f210";
}
.fa-icon-forumbee:before {
  content: "\f211";
}
.fa-icon-leanpub:before {
  content: "\f212";
}
.fa-icon-sellsy:before {
  content: "\f213";
}
.fa-icon-shirtsinbulk:before {
  content: "\f214";
}
.fa-icon-simplybuilt:before {
  content: "\f215";
}
.fa-icon-skyatlas:before {
  content: "\f216";
}
.fa-icon-cart-plus:before {
  content: "\f217";
}
.fa-icon-cart-arrow-down:before {
  content: "\f218";
}
.fa-icon-diamond:before {
  content: "\f219";
}
.fa-icon-ship:before {
  content: "\f21a";
}
.fa-icon-user-secret:before {
  content: "\f21b";
}
.fa-icon-motorcycle:before {
  content: "\f21c";
}
.fa-icon-street-view:before {
  content: "\f21d";
}
.fa-icon-heartbeat:before {
  content: "\f21e";
}
.fa-icon-venus:before {
  content: "\f221";
}
.fa-icon-mars:before {
  content: "\f222";
}
.fa-icon-mercury:before {
  content: "\f223";
}
.fa-icon-intersex:before,
.fa-icon-transgender:before {
  content: "\f224";
}
.fa-icon-transgender-alt:before {
  content: "\f225";
}
.fa-icon-venus-double:before {
  content: "\f226";
}
.fa-icon-mars-double:before {
  content: "\f227";
}
.fa-icon-venus-mars:before {
  content: "\f228";
}
.fa-icon-mars-stroke:before {
  content: "\f229";
}
.fa-icon-mars-stroke-v:before {
  content: "\f22a";
}
.fa-icon-mars-stroke-h:before {
  content: "\f22b";
}
.fa-icon-neuter:before {
  content: "\f22c";
}
.fa-icon-genderless:before {
  content: "\f22d";
}
.fa-icon-facebook-official:before {
  content: "\f230";
}
.fa-icon-pinterest-p:before {
  content: "\f231";
}
.fa-icon-whatsapp:before {
  content: "\f232";
}
.fa-icon-server:before {
  content: "\f233";
}
.fa-icon-user-plus:before {
  content: "\f234";
}
.fa-icon-user-times:before {
  content: "\f235";
}
.fa-icon-hotel:before,
.fa-icon-bed:before {
  content: "\f236";
}
.fa-icon-viacoin:before {
  content: "\f237";
}
.fa-icon-train:before {
  content: "\f238";
}
.fa-icon-subway:before {
  content: "\f239";
}
.fa-icon-medium:before {
  content: "\f23a";
}
.fa-icon-yc:before,
.fa-icon-y-combinator:before {
  content: "\f23b";
}
.fa-icon-optin-monster:before {
  content: "\f23c";
}
.fa-icon-opencart:before {
  content: "\f23d";
}
.fa-icon-expeditedssl:before {
  content: "\f23e";
}
.fa-icon-battery-4:before,
.fa-icon-battery-full:before {
  content: "\f240";
}
.fa-icon-battery-3:before,
.fa-icon-battery-three-quarters:before {
  content: "\f241";
}
.fa-icon-battery-2:before,
.fa-icon-battery-half:before {
  content: "\f242";
}
.fa-icon-battery-1:before,
.fa-icon-battery-quarter:before {
  content: "\f243";
}
.fa-icon-battery-0:before,
.fa-icon-battery-empty:before {
  content: "\f244";
}
.fa-icon-mouse-pointer:before {
  content: "\f245";
}
.fa-icon-i-cursor:before {
  content: "\f246";
}
.fa-icon-object-group:before {
  content: "\f247";
}
.fa-icon-object-ungroup:before {
  content: "\f248";
}
.fa-icon-sticky-note:before {
  content: "\f249";
}
.fa-icon-sticky-note-o:before {
  content: "\f24a";
}
.fa-icon-cc-jcb:before {
  content: "\f24b";
}
.fa-icon-cc-diners-club:before {
  content: "\f24c";
}
.fa-icon-clone:before {
  content: "\f24d";
}
.fa-icon-balance-scale:before {
  content: "\f24e";
}
.fa-icon-hourglass-o:before {
  content: "\f250";
}
.fa-icon-hourglass-1:before,
.fa-icon-hourglass-start:before {
  content: "\f251";
}
.fa-icon-hourglass-2:before,
.fa-icon-hourglass-half:before {
  content: "\f252";
}
.fa-icon-hourglass-3:before,
.fa-icon-hourglass-end:before {
  content: "\f253";
}
.fa-icon-hourglass:before {
  content: "\f254";
}
.fa-icon-hand-grab-o:before,
.fa-icon-hand-rock-o:before {
  content: "\f255";
}
.fa-icon-hand-stop-o:before,
.fa-icon-hand-paper-o:before {
  content: "\f256";
}
.fa-icon-hand-scissors-o:before {
  content: "\f257";
}
.fa-icon-hand-lizard-o:before {
  content: "\f258";
}
.fa-icon-hand-spock-o:before {
  content: "\f259";
}
.fa-icon-hand-pointer-o:before {
  content: "\f25a";
}
.fa-icon-hand-peace-o:before {
  content: "\f25b";
}
.fa-icon-trademark:before {
  content: "\f25c";
}
.fa-icon-registered:before {
  content: "\f25d";
}
.fa-icon-creative-commons:before {
  content: "\f25e";
}
.fa-icon-gg:before {
  content: "\f260";
}
.fa-icon-gg-circle:before {
  content: "\f261";
}
.fa-icon-tripadvisor:before {
  content: "\f262";
}
.fa-icon-odnoklassniki:before {
  content: "\f263";
}
.fa-icon-odnoklassniki-square:before {
  content: "\f264";
}
.fa-icon-get-pocket:before {
  content: "\f265";
}
.fa-icon-wikipedia-w:before {
  content: "\f266";
}
.fa-icon-safari:before {
  content: "\f267";
}
.fa-icon-chrome:before {
  content: "\f268";
}
.fa-icon-firefox:before {
  content: "\f269";
}
.fa-icon-opera:before {
  content: "\f26a";
}
.fa-icon-internet-explorer:before {
  content: "\f26b";
}
.fa-icon-tv:before,
.fa-icon-television:before {
  content: "\f26c";
}
.fa-icon-contao:before {
  content: "\f26d";
}
.fa-icon-500px:before {
  content: "\f26e";
}
.fa-icon-amazon:before {
  content: "\f270";
}
.fa-icon-calendar-plus-o:before {
  content: "\f271";
}
.fa-icon-calendar-minus-o:before {
  content: "\f272";
}
.fa-icon-calendar-times-o:before {
  content: "\f273";
}
.fa-icon-calendar-check-o:before {
  content: "\f274";
}
.fa-icon-industry:before {
  content: "\f275";
}
.fa-icon-map-pin:before {
  content: "\f276";
}
.fa-icon-map-signs:before {
  content: "\f277";
}
.fa-icon-map-o:before {
  content: "\f278";
}
.fa-icon-map:before {
  content: "\f279";
}
.fa-icon-commenting:before {
  content: "\f27a";
}
.fa-icon-commenting-o:before {
  content: "\f27b";
}
.fa-icon-houzz:before {
  content: "\f27c";
}
.fa-icon-vimeo:before {
  content: "\f27d";
}
.fa-icon-black-tie:before {
  content: "\f27e";
}
.fa-icon-fonticons:before {
  content: "\f280";
}
.fa-icon-reddit-alien:before {
  content: "\f281";
}
.fa-icon-edge:before {
  content: "\f282";
}
.fa-icon-credit-card-alt:before {
  content: "\f283";
}
.fa-icon-codiepie:before {
  content: "\f284";
}
.fa-icon-modx:before {
  content: "\f285";
}
.fa-icon-fort-awesome:before {
  content: "\f286";
}
.fa-icon-usb:before {
  content: "\f287";
}
.fa-icon-product-hunt:before {
  content: "\f288";
}
.fa-icon-mixcloud:before {
  content: "\f289";
}
.fa-icon-scribd:before {
  content: "\f28a";
}
.fa-icon-pause-circle:before {
  content: "\f28b";
}
.fa-icon-pause-circle-o:before {
  content: "\f28c";
}
.fa-icon-stop-circle:before {
  content: "\f28d";
}
.fa-icon-stop-circle-o:before {
  content: "\f28e";
}
.fa-icon-shopping-bag:before {
  content: "\f290";
}
.fa-icon-shopping-basket:before {
  content: "\f291";
}
.fa-icon-hashtag:before {
  content: "\f292";
}
.fa-icon-bluetooth:before {
  content: "\f293";
}
.fa-icon-bluetooth-b:before {
  content: "\f294";
}
.fa-icon-percent:before {
  content: "\f295";
}
