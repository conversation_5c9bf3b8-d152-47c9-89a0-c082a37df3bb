from django.core.mail import send_mail
import logging

logger = logging.getLogger(__name__)


def send_simple_activation_email(user_email, subject, message, from_email):
    """
    Simple, fast email sending function that doesn't block registration
    """
    try:
        # Use Django's simple send_mail with fail_silently=True
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=True,  # Don't raise exceptions
        )
        logger.info(f"Activation email sent to {user_email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send activation email to {user_email}: {e}")
        return False


def send_simple_notification_email(recipient_email, subject, message, from_email):
    """
    Simple notification email sending
    """
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[recipient_email],
            fail_silently=True,
        )
        logger.info(f"Notification email sent to {recipient_email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send notification email to {recipient_email}: {e}")
        return False