{"info": {"_postman_id": "mog-dynamics-api-collection", "name": "MOG Dynamics API Collection", "description": "Complete API collection for MOG Dynamics logistics platform - Flutter backend testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "SecurePassword123!", "type": "text"}, {"key": "confirm_password", "value": "SecurePassword123!", "type": "text"}, {"key": "gender", "value": "male", "type": "text"}, {"key": "phone_no", "value": "+2348012345678", "type": "text"}, {"key": "address", "value": "123 Main Street", "type": "text"}, {"key": "city", "value": "Lagos", "type": "text"}, {"key": "state", "value": "Lagos", "type": "text"}, {"key": "country", "value": "Nigeria", "type": "text"}, {"key": "document_type", "value": "passport", "type": "text"}, {"key": "document", "type": "file", "src": []}, {"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/auth/register/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register", ""]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.refresh_token);", "        console.log('<PERSON><PERSON><PERSON> saved successfully');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login", ""]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.access) {", "        pm.collectionVariables.set('access_token', response.access);", "        console.log('Access token refreshed');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh", ""]}}}, {"name": "Account Activation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"uidb64\": \"your_uidb64_here\",\n  \"token\": \"your_token_here\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/activate/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "activate", ""]}}}]}, {"name": "User Management", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", ""]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "phone_no", "value": "+*************", "type": "text"}, {"key": "address", "value": "456 Updated Street", "type": "text"}, {"key": "city", "value": "<PERSON>ja", "type": "text"}, {"key": "state", "value": "FCT", "type": "text"}, {"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", ""]}}}, {"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/dashboard/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "dashboard", ""]}}}]}, {"name": "Import Shipments", "item": [{"name": "Create Import Shipment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sender\": \"{{sender_address_id}}\",\n  \"destination\": \"{{destination_address_id}}\",\n  \"phone_no\": \"+2348012345678\",\n  \"weight\": 15.0,\n  \"weight_unit\": \"Kg\",\n  \"lenght\": 80.0,\n  \"breadth\": 40.0,\n  \"height\": 25.0,\n  \"attention\": \"Perishable goods - handle quickly\",\n  \"items\": [\n    {\n      \"name\": \"Food Items\",\n      \"quantity\": 5,\n      \"price\": 200.00,\n      \"weight\": 10.0\n    },\n    {\n      \"name\": \"Spices\",\n      \"quantity\": 3,\n      \"price\": 150.00,\n      \"weight\": 5.0\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/imports/", "host": ["{{base_url}}"], "path": ["api", "v1", "imports", ""]}}}, {"name": "Get Import List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/imports/list/?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "imports", "list", ""], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Import Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/imports/{{import_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "imports", "{{import_id}}", ""]}}}, {"name": "Get Import Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/imports/stats/", "host": ["{{base_url}}"], "path": ["api", "v1", "imports", "stats", ""]}}}]}, {"name": "Local Logistics", "item": [{"name": "Create Logistics Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sender\": \"{{sender_address_id}}\",\n  \"receiver_name\": \"<PERSON>\",\n  \"receiver_address\": \"456 Delivery Street, Ikeja\",\n  \"receiver_city\": \"Lagos\",\n  \"receiver_state\": \"Lagos\",\n  \"phone_no\": \"+2348087654321\",\n  \"weight\": 10.0,\n  \"weight_unit\": \"Kg\",\n  \"lenght\": 60.0,\n  \"breadth\": 30.0,\n  \"height\": 20.0,\n  \"attention\": \"Fragile - handle with care\",\n  \"items\": [\n    {\n      \"name\": \"Electronics\",\n      \"quantity\": 1,\n      \"price\": 25000.00,\n      \"weight\": 8.0\n    },\n    {\n      \"name\": \"Accessories\",\n      \"quantity\": 2,\n      \"price\": 5000.00,\n      \"weight\": 2.0\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/logistics/", "host": ["{{base_url}}"], "path": ["api", "v1", "logistics", ""]}}}, {"name": "Get Company Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/logistics/orders/?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "logistics", "orders", ""], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Logistics Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/logistics/{{logistics_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "logistics", "{{logistics_id}}", ""]}}}, {"name": "Get Dispatcher Assignments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/logistics/assignments/", "host": ["{{base_url}}"], "path": ["api", "v1", "logistics", "assignments", ""]}}}, {"name": "Update Delivery Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"delivery_status\": \"In Transit\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/logistics/{{logistics_id}}/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "logistics", "{{logistics_id}}", "status", ""]}}}]}, {"name": "Export Shipments", "item": [{"name": "Create Export Shipment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sender\": \"{{sender_address_id}}\",\n  \"destination\": \"{{destination_address_id}}\",\n  \"phone_no\": \"+2348012345678\",\n  \"weight\": 25.5,\n  \"weight_unit\": \"Kg\",\n  \"lenght\": 100.0,\n  \"breadth\": 50.0,\n  \"height\": 30.0,\n  \"attention\": \"Handle with care - fragile items\",\n  \"items\": [\n    {\n      \"name\": \"Electronics\",\n      \"quantity\": 2,\n      \"price\": 500.00,\n      \"weight\": 12.5\n    },\n    {\n      \"name\": \"Documents\",\n      \"quantity\": 1,\n      \"price\": 50.00,\n      \"weight\": 1.0\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/exports/", "host": ["{{base_url}}"], "path": ["api", "v1", "exports", ""]}}}, {"name": "Get Export List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/exports/list/?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "exports", "list", ""], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Export Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/exports/{{export_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "exports", "{{export_id}}", ""]}}}, {"name": "Get Export Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/exports/stats/", "host": ["{{base_url}}"], "path": ["api", "v1", "exports", "stats", ""]}}}]}, {"name": "Address Management", "item": [{"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"John Doe Home\",\n  \"address\": \"123 Main Street, Victoria Island\",\n  \"city\": \"Lagos\",\n  \"state\": \"Lagos\",\n  \"country\": \"Nigeria\",\n  \"phone_number\": \"+2348012345678\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses/", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", ""]}}}, {"name": "Get User Addresses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/addresses/", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", ""]}}}, {"name": "Get Address Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}", ""]}}}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"John Doe Office\",\n  \"address\": \"456 Business District, Ikoyi\",\n  \"city\": \"Lagos\",\n  \"state\": \"Lagos\",\n  \"country\": \"Nigeria\",\n  \"phone_number\": \"+*************\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}", ""]}}}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}", ""]}}}]}, {"name": "Company Management", "item": [{"name": "Register Company", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "business_name", "value": "Test Logistics Ltd", "type": "text"}, {"key": "rc_number", "value": "RC12345", "type": "text"}, {"key": "company_address", "value": "123 Business District, Lagos", "type": "text"}, {"key": "cac_certificate", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/companies/register/", "host": ["{{base_url}}"], "path": ["api", "v1", "companies", "register", ""]}}}, {"name": "Get Company Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/companies/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "companies", "profile", ""]}}}, {"name": "Update Company Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "business_name", "value": "Updated Logistics Ltd", "type": "text"}, {"key": "company_address", "value": "456 Updated Business District, Lagos", "type": "text"}, {"key": "cac_certificate", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/companies/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "companies", "profile", ""]}}}, {"name": "Get Company Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/companies/stats/", "host": ["{{base_url}}"], "path": ["api", "v1", "companies", "stats", ""]}}}, {"name": "Check Company Verification", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/companies/verification-status/", "host": ["{{base_url}}"], "path": ["api", "v1", "companies", "verification-status", ""]}}}]}, {"name": "Payment Processing", "item": [{"name": "Initiate Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipment_type\": \"export\",\n  \"shipment_id\": \"your-shipment-uuid-here\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/initiate/", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "initiate", ""]}}}, {"name": "Verify Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference\": \"payment-reference-from-paystack\",\n  \"shipment_type\": \"export\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/verify/", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "verify", ""]}}}]}, {"name": "Shipment Tracking", "item": [{"name": "Unified Tracking", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tracking/{{tracking_number}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "tracking", "{{tracking_number}}", ""]}}}, {"name": "Track Export Shipment", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tracking/export/{{tracking_number}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "tracking", "export", "{{tracking_number}}", ""]}}}, {"name": "Track Import Shipment", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tracking/import/{{tracking_number}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "tracking", "import", "{{tracking_number}}", ""]}}}, {"name": "Track Logistics Order", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tracking/logistics/{{tracking_number}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "tracking", "logistics", "{{tracking_number}}", ""]}}}]}, {"name": "Static Data", "item": [{"name": "Get Pricing Information", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/static/pricing/", "host": ["{{base_url}}"], "path": ["api", "v1", "static", "pricing", ""]}}}, {"name": "Get Gallery Images", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/static/galleries/", "host": ["{{base_url}}"], "path": ["api", "v1", "static", "galleries", ""]}}}, {"name": "Get App Configuration", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/static/config/", "host": ["{{base_url}}"], "path": ["api", "v1", "static", "config", ""]}}}]}]}