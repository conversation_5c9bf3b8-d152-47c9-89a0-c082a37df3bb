from rest_framework import serializers
from .models import Pricing


class PricingSerializer(serializers.ModelSerializer):
    """Serializer for Pricing model"""
    
    class Meta:
        model = Pricing
        fields = ['id', 'title', 'image', 'created_on', 'updated_on']
        read_only_fields = ['id', 'created_on', 'updated_on']
    
    def to_representation(self, instance):
        """Custom representation to include full image URL"""
        data = super().to_representation(instance)
        if instance.image:
            data['image'] = instance.image.url
        return data
