from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .api_views import (
    UserRegistrationAPIView,
    UserLoginAPIView,
    UserProfileAPIView,
    UserDashboardAPIView,
    AccountActivationAPIView
)

urlpatterns = [
    # Authentication endpoints
    path('auth/register/', UserRegistrationAPIView.as_view(), name='api_user_register'),
    path('auth/login/', UserLoginAPIView.as_view(), name='api_user_login'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='api_token_refresh'),
    path('auth/activate/', AccountActivationAPIView.as_view(), name='api_account_activation'),
    
    # User management endpoints
    path('users/profile/', UserProfileAPIView.as_view(), name='api_user_profile'),
    path('users/dashboard/', UserDashboardAPIView.as_view(), name='api_user_dashboard'),
]
