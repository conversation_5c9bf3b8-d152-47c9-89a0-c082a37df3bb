from django.db import models
from django.urls import reverse
from apps.common import choices
from apps.users.models import CustomUser
from apps.addresses.models import Address
from djmoney.models.fields import MoneyField
from apps.common.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField
from apps.exports.models import Export
from apps.imports.models import Import


class ConsolidatedItem(TimeStampedModel):
    """
    Consolidated model that holds all items from both exports and imports
    """
    ITEM_TYPE_CHOICES = [
        ('export', 'Export Item'),
        ('import', 'Import Item'),
    ]
    
    # Basic item information
    name = models.CharField(max_length=255)
    quantity = models.IntegerField(default=1)
    price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD')
    total_price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD', blank=True, null=True)
    weight = models.FloatField()
    
    # Type and source tracking
    item_type = models.CharField(max_length=10, choices=ITEM_TYPE_CHOICES)
    
    # Foreign keys to original items (only one will be populated)
    export = models.ForeignKey(Export, on_delete=models.CASCADE, null=True, blank=True)
    import_shipment = models.ForeignKey(Import, on_delete=models.CASCADE, null=True, blank=True)
    
    # Consolidated shipment information (denormalized for easy access)
    customer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, null=True, blank=True)
    sender = models.ForeignKey(
        Address, on_delete=models.SET_NULL,
        related_name="consolidated_item_sender", null=True, blank=True
    )
    destination = models.ForeignKey(
        Address, on_delete=models.SET_NULL,
        related_name="consolidated_item_destination", null=True, blank=True
    )
    phone_no = PhoneNumberField(blank=True, max_length=14)
    tracking_no = models.CharField(max_length=50, db_index=True)
    payment_status = models.CharField(choices=choices.shipping_payment_choices, default="Unpaid", max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(null=True, blank=True)
    shipping_status = models.CharField(default="Pending", max_length=255, choices=choices.shipping_status_choices)
    
    # Shipment dimensions and weight
    shipment_weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    length = models.FloatField(default=0.00, blank=True)
    breadth = models.FloatField(default=0.00, blank=True)
    height = models.FloatField(default=0.00, blank=True)
    volumetric_weight = models.FloatField(default=0.00, blank=True)
    volumetric_weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    
    # Fees
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    class Meta:
        verbose_name = "Consolidated Item"
        verbose_name_plural = "Consolidated Items"
        ordering = ('-created_on',)
        indexes = [
            models.Index(fields=['item_type']),
            models.Index(fields=['tracking_no']),
            models.Index(fields=['customer']),
            models.Index(fields=['shipping_status']),
            models.Index(fields=['payment_status']),
        ]

    def __str__(self):
        return f"{self.get_item_type_display()} - {self.name} ({self.tracking_no})"

    def get_absolute_url(self):
        if self.item_type == 'export':
            return reverse("export_detail", kwargs={"id": self.export.id})
        else:
            return reverse("import_detail", kwargs={"id": self.import_shipment.id})

    @property
    def get_shipment_weight(self):
        return f"{self.shipment_weight} {self.weight_unit}"

    @property
    def get_volumetric_weight(self):
        return f"{self.volumetric_weight} {self.volumetric_weight_unit.capitalize()}"

    @property
    def customer_full_name(self):
        return f"{self.customer.first_name} {self.customer.last_name}"

    @property
    def source_object(self):
        """Returns the original export or import object"""
        if self.item_type == 'export':
            return self.export
        else:
            return self.import_shipment

    def save(self, *args, **kwargs):
        # Calculate total price
        if self.price and self.quantity:
            self.total_price = self.price * self.quantity
        
        # Calculate volumetric weight if dimensions are provided
        if self.length and self.breadth and self.height:
            self.volumetric_weight = round((self.length * self.breadth * self.height) / 5000, 2)
        
        return super().save(*args, **kwargs)

    @classmethod
    def sync_from_export_item(cls, export_item):
        """Create or update consolidated item from export item"""
        export_obj = export_item.export  # This is the Export object related to the ExportItem
        
        # Safely get values, providing defaults if export_obj or its related fields are None
        customer_val = export_obj.customer if export_obj and export_obj.customer else None
        sender_val = export_obj.sender if export_obj and export_obj.sender else None
        destination_val = export_obj.destination if export_obj and export_obj.destination else None
        phone_no_val = export_obj.phone_no if export_obj else ''
        tracking_no_val = export_obj.tracking_no if export_obj else ''
        payment_status_val = export_obj.payment_status if export_obj else "Unpaid"
        payment_reference_val = export_obj.payment_reference if export_obj else None
        shipped_at_val = export_obj.shipped_at if export_obj else None
        shipping_status_val = export_obj.shipping_status if export_obj else "Pending"
        shipment_weight_val = export_obj.weight if export_obj else None  # Assuming export_obj.weight refers to main shipment weight
        weight_unit_val = export_obj.weight_unit if export_obj else "Kg"
        
        # Handle potential typos/missing attributes gracefully for dimensions and fees
        length_val = getattr(export_obj, 'lenght', 0.00) if export_obj else 0.00  # Assuming 'lenght' is the field name on Export model
        breadth_val = getattr(export_obj, 'breadth', 0.00) if export_obj else 0.00
        height_val = getattr(export_obj, 'height', 0.00) if export_obj else 0.00
        volumetric_weight_val = getattr(export_obj, 'volumentary_weight', 0.00) if export_obj else 0.00 # Assuming 'volumentary_weight'
        volumetric_weight_unit_val = getattr(export_obj, 'volumentary_weight_unit', "Kg") if export_obj else "Kg"
        shipping_fee_val = getattr(export_obj, 'export_fee', None) if export_obj else None

        consolidated_item, created = cls.objects.get_or_create(
            export=export_obj,
            name=export_item.name,  # Using name as part of get_or_create criteria is fine if unique within an export
            defaults={
                'item_type': 'export',
                'quantity': export_item.quantity,
                'price': export_item.price,
                'total_price': export_item.total_price,
                'weight': export_item.weight,
                'customer': customer_val,
                'sender': sender_val,
                'destination': destination_val,
                'phone_no': phone_no_val,
                'tracking_no': tracking_no_val,
                'payment_status': payment_status_val,
                'payment_reference': payment_reference_val,
                'shipped_at': shipped_at_val,
                'shipping_status': shipping_status_val,
                'shipment_weight': shipment_weight_val,
                'weight_unit': weight_unit_val,
                'length': length_val,
                'breadth': breadth_val,
                'height': height_val,
                'volumetric_weight': volumetric_weight_val,
                'volumetric_weight_unit': volumetric_weight_unit_val,
                'shipping_fee': shipping_fee_val,
            }
        )
        
        if not created:
            # Update existing record --
            consolidated_item.quantity = export_item.quantity
            consolidated_item.price = export_item.price
            consolidated_item.total_price = export_item.total_price
            consolidated_item.weight = export_item.weight
            consolidated_item.customer = customer_val
            consolidated_item.sender = sender_val
            consolidated_item.destination = destination_val
            consolidated_item.phone_no = phone_no_val
            consolidated_item.tracking_no = tracking_no_val
            consolidated_item.payment_status = payment_status_val
            consolidated_item.payment_reference = payment_reference_val
            consolidated_item.shipped_at = shipped_at_val
            consolidated_item.shipping_status = shipping_status_val
            consolidated_item.shipment_weight = shipment_weight_val
            consolidated_item.weight_unit = weight_unit_val
            consolidated_item.length = length_val
            consolidated_item.breadth = breadth_val
            consolidated_item.height = height_val
            consolidated_item.volumetric_weight = volumetric_weight_val
            consolidated_item.volumetric_weight_unit = volumetric_weight_unit_val
            consolidated_item.shipping_fee = shipping_fee_val
            consolidated_item.save()
        
        return consolidated_item

    @classmethod
    def sync_from_import_item(cls, import_item):
        """Create or update consolidated item from import item"""
        import_obj = import_item.imports # This is the Import object related to the ImportItem
        
        # Safely get values, providing defaults if import_obj or its related fields are None
        customer_val = import_obj.customer if import_obj and import_obj.customer else None
        sender_val = import_obj.sender if import_obj and import_obj.sender else None
        destination_val = import_obj.destination if import_obj and import_obj.destination else None
        phone_no_val = import_obj.phone_no if import_obj else ''
        tracking_no_val = import_obj.tracking_no if import_obj else ''
        payment_status_val = import_obj.payment_status if import_obj else "Unpaid"
        payment_reference_val = import_obj.payment_reference if import_obj else None
        shipped_at_val = import_obj.shipped_at if import_obj else None
        shipping_status_val = import_obj.shipping_status if import_obj else "Pending"
        shipment_weight_val = import_obj.weight if import_obj else None # Assuming import_obj.weight refers to main shipment weight
        weight_unit_val = import_obj.weight_unit if import_obj else "Kg"

        # Handle potential typos/missing attributes gracefully for dimensions and fees
        length_val = getattr(import_obj, 'lenght', 0.00) if import_obj else 0.00 # Assuming 'lenght' is the field name on Import model
        breadth_val = getattr(import_obj, 'breadth', 0.00) if import_obj else 0.00
        height_val = getattr(import_obj, 'height', 0.00) if import_obj else 0.00
        volumetric_weight_val = getattr(import_obj, 'volumentary_weight', 0.00) if import_obj else 0.00 # Assuming 'volumentary_weight'
        volumetric_weight_unit_val = getattr(import_obj, 'volumentary_weight_unit', "Kg") if import_obj else "Kg"
        shipping_fee_val = getattr(import_obj, 'import_fee', None) if import_obj else None

        consolidated_item, created = cls.objects.get_or_create(
            import_shipment=import_obj,
            name=import_item.name, # Using name as part of get_or_create criteria is fine if unique within an import
            defaults={
                'item_type': 'import',
                'quantity': import_item.quantity,
                'price': import_item.price,
                'total_price': import_item.total_price,
                'weight': import_item.weight,
                'customer': customer_val,
                'sender': sender_val,
                'destination': destination_val,
                'phone_no': phone_no_val,
                'tracking_no': tracking_no_val,
                'payment_status': payment_status_val,
                'payment_reference': payment_reference_val,
                'shipped_at': shipped_at_val,
                'shipping_status': shipping_status_val,
                'shipment_weight': shipment_weight_val,
                'weight_unit': weight_unit_val,
                'length': length_val,
                'breadth': breadth_val,
                'height': height_val,
                'volumetric_weight': volumetric_weight_val,
                'volumetric_weight_unit': volumetric_weight_unit_val,
                'shipping_fee': shipping_fee_val,
            }
        )
        
        if not created:
            # Update existing record
            consolidated_item.quantity = import_item.quantity
            consolidated_item.price = import_item.price
            consolidated_item.total_price = import_item.total_price
            consolidated_item.weight = import_item.weight
            consolidated_item.customer = customer_val
            consolidated_item.sender = sender_val
            consolidated_item.destination = destination_val
            consolidated_item.phone_no = phone_no_val
            consolidated_item.tracking_no = tracking_no_val
            consolidated_item.payment_status = payment_status_val
            consolidated_item.payment_reference = payment_reference_val
            consolidated_item.shipped_at = shipped_at_val
            consolidated_item.shipping_status = shipping_status_val
            consolidated_item.shipment_weight = shipment_weight_val
            consolidated_item.weight_unit = weight_unit_val
            consolidated_item.length = length_val
            consolidated_item.breadth = breadth_val
            consolidated_item.height = height_val
            consolidated_item.volumetric_weight = volumetric_weight_val
            consolidated_item.volumetric_weight_unit = volumetric_weight_unit_val
            consolidated_item.shipping_fee = shipping_fee_val
            consolidated_item.save()
        
        return consolidated_item