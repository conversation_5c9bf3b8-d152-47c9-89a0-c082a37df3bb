{% extends "base.html" %}
{% load crispy_forms_tags %}
{% block title %}Reset your new password{% endblock %}


{% block content %}
    <div class="section-padding-70">
        <div class="container">
            <div class="row">
                <div class="col-11 col-md-offset-3 col-sm-6 col-sm-offset-2">
                    {% if validlink %}
                        <form method="post" class="registration">
                            <h1 class="text-center">Set your new password</h1>
                            {% csrf_token %}
                            <div class="">
                                {{ form|crispy }}
                            </div>
                            <input class="btn btn-success bg-black text-white w-100" type="submit" value="Change my password"/>
                        </form>
                    {% else %}
                        <p>
                            <h1>Expired reset link.</h1>
                            The password reset link was invalid, possibly because it has
                            already been used and has expired. Please request a new <a href="{% url 'custom_password_reset' %}">Password reset link here</a>
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}