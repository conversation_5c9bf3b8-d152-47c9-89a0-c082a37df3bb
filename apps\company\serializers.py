from rest_framework import serializers
from .models import Company
from apps.users.models import CustomUser


class CompanyRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for company registration"""
    
    class Meta:
        model = Company
        fields = [
            'business_name', 'rc_number', 'company_address', 'cac_certificate'
        ]
    
    def validate_rc_number(self, value):
        """Validate RC number format and uniqueness"""
        if len(value) != 7:
            raise serializers.ValidationError("RC number must be exactly 7 characters long.")
        
        if Company.objects.filter(rc_number=value).exists():
            raise serializers.ValidationError("A company with this RC number already exists.")
        
        return value
    
    def validate_business_name(self, value):
        """Validate business name uniqueness"""
        if Company.objects.filter(business_name__iexact=value).exists():
            raise serializers.ValidationError("A company with this business name already exists.")
        
        return value
    
    def create(self, validated_data):
        """Create company for current user"""
        user = self.context['request'].user
        
        # Check if user already has a company
        if Company.objects.filter(user=user).exists():
            raise serializers.ValidationError("User already has a registered company.")
        
        return Company.objects.create(user=user, **validated_data)


class CompanySerializer(serializers.ModelSerializer):
    """Serializer for Company model"""
    user = serializers.SerializerMethodField()
    cac_certificate_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = [
            'id', 'business_name', 'rc_number', 'company_address',
            'cac_certificate', 'cac_certificate_url', 'user',
            'created_on', 'updated_on'
        ]
        read_only_fields = ['id', 'user', 'created_on', 'updated_on', 'cac_certificate_url']
    
    def get_user(self, obj):
        """Get user info"""
        return {
            'id': str(obj.user.id),
            'name': f"{obj.user.first_name} {obj.user.last_name}",
            'email': obj.user.email
        }
    
    def get_cac_certificate_url(self, obj):
        """Get CAC certificate URL"""
        if obj.cac_certificate:
            return obj.cac_certificate.url
        return None


class CompanyUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating company information"""
    
    class Meta:
        model = Company
        fields = [
            'business_name', 'company_address', 'cac_certificate'
        ]
        # RC number should not be updatable after registration
    
    def validate_business_name(self, value):
        """Validate business name uniqueness (excluding current instance)"""
        instance = self.instance
        if Company.objects.filter(business_name__iexact=value).exclude(id=instance.id).exists():
            raise serializers.ValidationError("A company with this business name already exists.")
        
        return value


class CompanyStatsSerializer(serializers.Serializer):
    """Serializer for company statistics"""
    total_logistics_orders = serializers.IntegerField()
    pending_orders = serializers.IntegerField()
    in_transit_orders = serializers.IntegerField()
    delivered_orders = serializers.IntegerField()
    unpaid_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    recent_orders = serializers.ListField()
