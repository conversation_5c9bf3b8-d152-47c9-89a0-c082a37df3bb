[flake8]
max-line-length = 120
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs,node_modules,venv

[pycodestyle]
max-line-length = 120
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs,node_modules,venv

[mypy]
python_version = 3.10
check_untyped_defs = True
ignore_missing_imports = True
warn_unused_ignores = True
warn_redundant_casts = True
warn_unused_configs = True
plugins = mypy_django_plugin.main

[mypy.plugins.django-stubs]
django_settings_module = config.settings.test

[mypy-*.migrations.*]
# Django migrations should not produce any errors:
ignore_errors = True

[coverage:run]
include = vigolend/*
omit = *migrations*, *tests* , *venv*
plugins =
    django_coverage_plugin