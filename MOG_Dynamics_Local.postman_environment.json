{"id": "mog-dynamics-local-env", "name": "MOG Dynamics - Local Development", "values": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "export_id", "value": "", "type": "default", "enabled": true}, {"key": "import_id", "value": "", "type": "default", "enabled": true}, {"key": "logistics_id", "value": "", "type": "default", "enabled": true}, {"key": "address_id", "value": "", "type": "default", "enabled": true}, {"key": "company_id", "value": "", "type": "default", "enabled": true}, {"key": "tracking_number", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}