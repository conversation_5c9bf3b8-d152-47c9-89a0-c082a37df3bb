import os
from celery import Celery


os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings.base")
app = Celery("core")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()


# @app.task(bind=True, ignore_result=True)
# def debug_task(self):
#     print(f'Request: {self.request!r}')

# import os
# from celery import Celery
# from django.apps import apps
# from django.apps.config import AppConfig
# from django.conf import settings
# from django_redis import get_redis_connection


# if not settings.configured:
#     # Set the default Django settings module for the 'celery' program.
#     os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings.base")

# app = Celery("core")


# class CeleryConfig(AppConfig):
#     name = "core"
#     verbose_name = "Celery Config"

#     def ready(self):
#         # Using a string here means the worker will not have to
#         # pickle the object when using Windows.
#         app.config_from_object("django.conf:settings", namespace="CELERY")
#         installed_apps = [app_config.name for app_config in apps.get_app_configs()]
#         app.autodiscover_tasks(installed_apps, force=True)

#     def tearDown(self):
#         get_redis_connection("default").flushall()
#         print("Cache Flushed!!")
