import requests
from core.settings import base
from .models import Import, Item
from django.views import generic
from django.contrib import messages
from .paystack import initiate_payment
from django.core.paginator import Paginator
from django.db.models import Sum
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.db import transaction


@login_required(login_url="user_login")
def import_list(request):
    """
    Returns list of Import objects including paginating the 
    lists in situations where the table grows.
    """
    template_name = "imports/import_list.html"
    total_imports = Import.objects.select_related('customer', 'sender').filter(customer=request.user).count()
    import_list = Import.objects.select_related('customer', 'sender').filter(customer=request.user)

    paginator = Paginator(import_list, 10)
    page = request.GET.get('page')
    imports = paginator.get_page(page)

    context = {"total_imports": total_imports, "imports": imports}
    return render(request, template_name, context)


import_obj = None


@login_required(login_url="user_login")
def import_detail(request, id):
    """
    Returns list of items and relating iformation about the Import object.
    including paginating the lists in situations where the table grows
    """
    template_name = "imports/import_detail.html"
    imports_obj = get_object_or_404(Import, id=id)

    global import_obj
    import_obj = imports_obj
    total_items = Item.objects.select_related('imports').filter(
        imports=imports_obj).aggregate(total_items=Sum('quantity'))["total_items"]

    items_list = Item.objects.filter(imports=imports_obj).order_by("created_on")
    paginator = Paginator(items_list, 5)
    page = request.GET.get('page')
    items = paginator.get_page(page)
    context = {"import": imports_obj, "items": items, "total_items": total_items}
    return render(request, template_name, context)


# Initial payment
@login_required(login_url="user_login")
@transaction.atomic
def payment_view(request):
    template_name = "imports/payment.html"
    if request.method == 'GET':
        response = initiate_payment(request, import_obj)
        return response
    else:
        return render(request, template_name)


# Verify payment status
@login_required(login_url="user_login")
@transaction.atomic
def verify_payment(request):
    transaction_reference = request.GET.get("reference")

    paystack_live_secret_key = str(base.PAYSTACK_LIVE_SECRET_KEY)
    # paystack_test_secret_key = str(base.PAYSTACK_TEST_SECRET_KEY)
    headers = {"Authorization": f"Bearer {paystack_live_secret_key}"}
    url = f"{base.PAYSTACK_BASE_URL}verify/{transaction_reference}"
    response = requests.get(url, headers=headers, timeout=30)
    response_data = response.json()['data']
    import_fee = float(import_obj.import_fee * 100)

    if response_data['status'] == 'success' and response_data['amount'] == import_fee:
        Import.objects.select_for_update().filter(id=import_obj.id, payment_reference=transaction_reference).update(payment_status='paid')
        messages.success(request, f"payment transaction {response_data['status']}")
        return redirect("import_detail", import_obj.id)

    elif response_data['status'] == 'pending':
        Import.objects.select_for_update().filter(id=import_obj.id, payment_reference=transaction_reference).update(payment_status='pending')
        messages.info(request, f"transaction transaction is {response_data['status']}")
        return redirect("import_detail", import_obj.id)

    if response_data['status'] == 'failed':
        Import.objects.select_for_update().filter(id=import_obj.id, payment_reference=transaction_reference).update(payment_status='failed')
        messages.error(request, f"payment transaction has {response_data['status']}")
        return redirect("import_detail", import_obj.id)

    messages.error(request, f"{response_data['status']}")
    return redirect("import_detail", import_obj.id)


# Track import shipping record.
class TrackImportShippingView(generic.TemplateView):
    template_name = "imports/import-tracking-page.html"


class ImportShippingResultView(generic.TemplateView):
    """
    Returns import shipping object from the search query.
    """
    model = Import
    template_name = "imports/import_search_result.html"

    def get(self, request):
        tracking_no = self.request.GET.get("tracking-no", "")
        if tracking_no != "":
            result_obj = self.model.objects.filter(tracking_no=tracking_no).first()
            if result_obj:
                return render(request, self.template_name, {"result_obj": result_obj})
            messages.error(request, "No matching record found")
            return render(request, self.template_name, {"result_obj": result_obj})
        messages.error(request, "You cannot submit an empty query.")
        return redirect("track_import_shipping")
