
# Django stuff:
local_settings.py
db.sqlite3
db.sqlite3-journal


# pyenv
.python-version

*.pyc

# Environments
.env
.venv
staticfiles/

env/
venv/
ENV/
env.bak/
venv.bak/
.example.env
# migrations
.profile_images
.user_documents

.__pycache__.py
.__pycache__
__pycache__
__pycache__/

*.sqlite3
.bootstrap
bootstrap/
*logs/

mog_dynamics_app

# Ignore compiled Python files and cache folders within migration directories
*/migrations/__pycache__/
*/migrations/*.pyc