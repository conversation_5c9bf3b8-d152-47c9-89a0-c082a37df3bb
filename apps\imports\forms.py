from django import forms
from apps.common import choices
from phonenumber_field.widgets import PhoneNumberPrefixWidget



class ImportForm(forms.Form):
    weight = forms.CharField(label="Items weight", max_length=100, required=True)
    sender_country = forms.ChoiceField(label="sender country", choices=choices.country_choices)
    receiver_country = forms.ChoiceField(label="receiver country", choices=choices.country_choices)
    phone_no = forms.CharField(label="phone number", max_length=10, required=True, widget=PhoneNumberPrefixWidget(initial="NG"))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['weight'].widget.attrs.update({'class': 'form-control mb-3', "placeholder":"Enter weight"})
        self.fields['sender_country'].widget.attrs.update({'class': 'form-control mb-3'})
        self.fields['receiver_country'].widget.attrs.update({'class': 'form-control mb-3'})
        self.fields['phone_no'].widget.attrs.update({'class': 'form-control mb-2', "placeholder":"10 digit phone number"})