{% load static %}
<!DOCTYPE html>
<html lang="en">

  <head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ScriptsBundle">
    <title>Mog Dynamics Logistic Company</title>
    <!-- =-=-=-=-=-=-= Favicons Icon =-=-=-=-=-=-= -->
    <link rel="icon" href="{% static 'media/images/favicon.ico' %}" type="image/x-icon" />
    <!-- =-=-=-=-=-=-= Mobile Specific =-=-=-=-=-=-= -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- =-=-=-=-=-=-= Bootstrap CSS Style =-=-=-=-=-=-= -->
    <link rel="stylesheet" href="{% static 'css/bootstrap.css' %}?{% now 'U' %}">
    <!-- =-=-=-=-=-=-= Template CSS Style =-=-=-=-=-=-= -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}?{% now 'U' %}">
    <!-- =-=-=-=-=-=-= Font Awesome =-=-=-=-=-=-= -->
    <link rel="stylesheet" href="{% static 'css/font-awesome.css' %}?{% now 'U' %}" type="text/css">
    <!-- =-=-=-=-=-=-= Et Line Fonts =-=-=-=-=-=-= -->
    <link rel="stylesheet" href="{% static 'css/et-line-fonts.css' %}" type="text/css">
    <!-- =-=-=-=-=-=-= Magnific PopUP CSS =-=-=-=-=-=-= -->
    <link href="{% static 'js/magnific-popup/magnific-popup.css' %}?{% now 'U' %}" rel="stylesheet">
    <!-- =-=-=-=-=-=-= Owl carousel =-=-=-=-=-=-= -->
    <link rel="stylesheet" type="text/css" href="{% static 'css/owl.carousel.css' %}?{% now 'U' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/owl.style.css' %}?{% now 'U' %}">
    <!-- =-=-=-=-=-=-= Google Fonts =-=-=-=-=-=-= -->
    <link href="http://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400italic,600,600italic,700,700italic,900italic,900,300,300italic" rel="stylesheet" type="text/css">
    <link href="http://fonts.googleapis.com/css?family=Merriweather:400,300,300italic,400italic,700,700italic" rel="stylesheet" type="text/css">
    <!-- =-=-=-=-=-=-= Flat Icon =-=-=-=-=-=-= -->
    <link href="{% static 'css/flaticon.css' %}?{% now 'U' %}" rel="stylesheet">
    <!-- Theme Color -->
    <link rel="stylesheet" id="color" href="{% static 'css/colors/defualt.css' %}?{% now 'U' %}">
    <!-- For Style Switcher -->
    <link rel="stylesheet" id="theme-color" type="text/css" href="#." />
    <!-- Animation Css -->
    <link href="{% static 'css/animate.min.css' %}?{% now 'U' %}" rel="stylesheet">
    <!-- Menu Hover -->
    <link href="{% static 'css/bootstrap-dropdownhover.min.css' %}?{% now 'U' %}" rel="stylesheet">
    <!-- =-=-=-=-=-=-= For online booking page Only =-=-=-=-=-=-= -->
    <link href="{% static 'css/select2.min.css' %}" rel="stylesheet" />
    <link href="{% static 'css/bootstrap-datetimepicker.min.css' %}" rel="stylesheet" />
  
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
        <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!--intlTelInput css-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css"/>
  </head>

  <body>
    <!-- =-=-=-=-=-=-= HEADER =-=-=-=-=-=-= -->
    <section class="top-bar mb-2 bg-black navbar">
      <div class="container-fluid">
        <div class="burger-menu nav-left mt-2 pull-left" onclick="toggleSidebar()">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
        </div>
          
        <!-- /.left-text -->
        {% if user.is_authenticated %}
          <ul class="nav-right pull-right list-unstyled">
            <li class="dropdown nav-profile"> 
              <a class="dropdown-toggle" data-hover="dropdown" data-toggle="dropdown" data-animations="fadeInUp">
                {% if user.profile_picture %}
                    <img class="img-circle resize" alt="" src="{{ user.profile_picture.url }}">
                {% else %}
                    <img class="img-circle resize" alt="" src="{% static 'media/images/dummy_qr.png' %}">
                {% endif %}

                {% if user.user_type == "small business" %}
                  <span class="hidden-xs small-padding"> <span>{{ user.get_business_name }}</span> <i class="fa fa-caret-down"></i></span>
                {% else %}
                  <span class="hidden-xs small-padding"> <span>{{ user.get_full_name|capfirst }}</span> <i class="fa fa-caret-down"></i></span>
                {% endif %}
                
              </a>
              {% if user.user_type == "dispatcher" %}
                <ul class="dropdown-menu with-arrow pull-right">
                  <li> <a href="{% url 'user_profile' %}"> <i class="fa fa-check"></i> <span>Tracking History</span> </a> </li>
                  <li> <a href="{% url 'user_logout' %}"> <i class="fa fa-sign-out"></i> <span>Log Out</span> </a> </li>
                </ul>
              {% else %}
                <ul class="dropdown-menu with-arrow pull-right">
                  <li> <a href="{% url 'user_profile' %}"> <i class="fa fa-user"></i> <span>My Profile</span> </a> </li>
                  {% if user.user_type == 'small business' %}
                    <li> <a href="{% url 'company_order_list' %}"> <i class="fa fa-check"></i> <span>Tracking History</span> </a> </li>
                  {% else %}
                    <li> <a href="{% url 'export_list' %}"> <i class="fa fa-check"></i> <span>Tracking History</span> </a> </li>
                  {% endif %}
                  <li> <a href="{% url 'user_logout' %}"> <i class="fa fa-sign-out"></i> <span>Log Out</span> </a> </li>
                </ul>
              {% endif %}
            </li>
          </ul>
        {% else %}
            <ul class="nav-right pull-right list-unstyled">
                <li> <a href="{% url 'user_login' %}"><i class="fa fa-lock"></i> Login </a></li>
                <li> <a href="{% url 'create_account' %}"><i class="fa fa-user"></i> Sign Up </a></li>
            </ul>
        {% endif %}
      </div>
    </section>

    
    <!--container-->
    <div class="container">
      <!--sidebar-->
      <div class="sidebar">
        <ul class="items flex-columns">
          {% if user.user_type == "small business" %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'home' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-house" viewBox="0 0 16 16">
                  <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                </svg><br>
                Home
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'company_order_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck" viewBox="0 0 16 16">
                  <path d="M0 3.5A1.5 1.5 0 0 1 1.5 2h9A1.5 1.5 0 0 1 12 3.5V5h1.02a1.5 1.5 0 0 1 1.17.563l1.481 1.85a1.5 1.5 0 0 1 .329.938V10.5a1.5 1.5 0 0 1-1.5 1.5H14a2 2 0 1 1-4 0H5a2 2 0 1 1-3.998-.085A1.5 1.5 0 0 1 0 10.5v-7zm1.294 7.456A1.999 1.999 0 0 1 4.732 11h5.536a2.01 2.01 0 0 1 .732-.732V3.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .294.456zM12 10a2 2 0 0 1 1.732 1h.768a.5.5 0 0 0 .5-.5V8.35a.5.5 0 0 0-.11-.312l-1.48-1.85A.5.5 0 0 0 13.02 6H12v4zm-9 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm9 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
                </svg><br>
                Local Logistics
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'export_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck" viewBox="0 0 16 16">
                  <path d="M0 3.5A1.5 1.5 0 0 1 1.5 2h9A1.5 1.5 0 0 1 12 3.5V5h1.02a1.5 1.5 0 0 1 1.17.563l1.481 1.85a1.5 1.5 0 0 1 .329.938V10.5a1.5 1.5 0 0 1-1.5 1.5H14a2 2 0 1 1-4 0H5a2 2 0 1 1-3.998-.085A1.5 1.5 0 0 1 0 10.5v-7zm1.294 7.456A1.999 1.999 0 0 1 4.732 11h5.536a2.01 2.01 0 0 1 .732-.732V3.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .294.456zM12 10a2 2 0 0 1 1.732 1h.768a.5.5 0 0 0 .5-.5V8.35a.5.5 0 0 0-.11-.312l-1.48-1.85A.5.5 0 0 0 13.02 6H12v4zm-9 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm9 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
                </svg><br>
                Exports
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'import_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck-front" viewBox="0 0 16 16">
                  <path d="M5 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm8 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm-6-1a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2H7ZM4 2a1 1 0 0 0-1 1v3.9c0 .625.562 1.092 1.17.994C5.075 7.747 6.792 7.5 8 7.5c1.208 0 2.925.247 3.83.394A1.008 1.008 0 0 0 13 6.9V3a1 1 0 0 0-1-1H4Zm0 1h8v3.9c0 .002 0 .001 0 0l-.002.004a.013.013 0 0 1-.005.002h-.004C11.088 6.761 9.299 6.5 8 6.5s-3.088.26-3.99.406h-.003a.013.013 0 0 1-.005-.002L4 6.9c0 .001 0 .002 0 0V3Z"/>
                  <path d="M1 2.5A2.5 2.5 0 0 1 3.5 0h9A2.5 2.5 0 0 1 15 2.5v9c0 .818-.393 1.544-1 2v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5V14H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2a2.496 2.496 0 0 1-1-2v-9ZM3.5 1A1.5 1.5 0 0 0 2 2.5v9A1.5 1.5 0 0 0 3.5 13h9a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 12.5 1h-9Z"/>
                </svg><br>
                Imports
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'user_profile' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-check" viewBox="0 0 16 16">
                  <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Zm1.679-4.493-1.335 2.226a.75.75 0 0 1-1.174.144l-.774-.773a.5.5 0 0 1 .708-.708l.547.548 1.17-1.951a.5.5 0 1 1 .858.514ZM11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"/>
                  <path d="M8.256 14a4.474 4.474 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10c.26 0 .507.009.74.025.226-.341.496-.65.804-.918C9.077 9.038 8.564 9 8 9c-5 0-6 3-6 4s1 1 1 1h5.256Z"/>
                </svg><br>
                Profile
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'processes' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-question" viewBox="0 0 16 16">
                  <path d="M8.05 9.6c.336 0 .504-.24.554-.627.04-.534.198-.815.847-1.26.673-.475 1.049-1.09 1.049-1.986 0-1.325-.92-2.227-2.262-2.227-1.02 0-1.792.492-2.1 1.29A1.71 1.71 0 0 0 6 5.48c0 .393.203.64.545.64.272 0 .455-.147.564-.51.158-.592.525-.915 1.074-.915.61 0 1.03.446 1.03 1.084 0 .563-.208.885-.822 1.325-.619.433-.926.914-.926 1.64v.111c0 .428.208.745.585.745z"/>
                  <path d="m10.273 2.513-.921-.944.715-.698.622.637.89-.011a2.89 2.89 0 0 1 2.924 2.924l-.01.89.636.622a2.89 2.89 0 0 1 0 4.134l-.637.622.011.89a2.89 2.89 0 0 1-2.924 2.924l-.89-.01-.622.636a2.89 2.89 0 0 1-4.134 0l-.622-.637-.89.011a2.89 2.89 0 0 1-2.924-2.924l.01-.89-.636-.622a2.89 2.89 0 0 1 0-4.134l.637-.622-.011-.89a2.89 2.89 0 0 1 2.924-2.924l.89.01.622-.636a2.89 2.89 0 0 1 4.134 0l-.715.698a1.89 1.89 0 0 0-2.704 0l-.92.944-1.32-.016a1.89 1.89 0 0 0-1.911 1.912l.016 1.318-.944.921a1.89 1.89 0 0 0 0 2.704l.944.92-.016 1.32a1.89 1.89 0 0 0 1.912 1.911l1.318-.016.921.944a1.89 1.89 0 0 0 2.704 0l.92-.944 1.32.016a1.89 1.89 0 0 0 1.911-1.912l-.016-1.318.944-.921a1.89 1.89 0 0 0 0-2.704l-.944-.92.016-1.32a1.89 1.89 0 0 0-1.912-1.911l-1.318.016z"/>
                  <path d="M7.001 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0z"/>
                </svg><br>
                How To
              </a>
            </li>
          {% elif user.user_type == "dispatcher" %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'home' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-house" viewBox="0 0 16 16">
                  <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                </svg><br>
                Home
              </a>
            </li>
            
            <li class="nav-item">
              <a class="nav-link" href="{% url 'local_order_delivery_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck" viewBox="0 0 16 16">
                  <path d="M0 3.5A1.5 1.5 0 0 1 1.5 2h9A1.5 1.5 0 0 1 12 3.5V5h1.02a1.5 1.5 0 0 1 1.17.563l1.481 1.85a1.5 1.5 0 0 1 .329.938V10.5a1.5 1.5 0 0 1-1.5 1.5H14a2 2 0 1 1-4 0H5a2 2 0 1 1-3.998-.085A1.5 1.5 0 0 1 0 10.5v-7zm1.294 7.456A1.999 1.999 0 0 1 4.732 11h5.536a2.01 2.01 0 0 1 .732-.732V3.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .294.456zM12 10a2 2 0 0 1 1.732 1h.768a.5.5 0 0 0 .5-.5V8.35a.5.5 0 0 0-.11-.312l-1.48-1.85A.5.5 0 0 0 13.02 6H12v4zm-9 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm9 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
                </svg><br>
                Local Logistics
              </a>
            </li>
          {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'home' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-house" viewBox="0 0 16 16">
                  <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"/>
                </svg><br>
                Home
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'export_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck" viewBox="0 0 16 16">
                  <path d="M0 3.5A1.5 1.5 0 0 1 1.5 2h9A1.5 1.5 0 0 1 12 3.5V5h1.02a1.5 1.5 0 0 1 1.17.563l1.481 1.85a1.5 1.5 0 0 1 .329.938V10.5a1.5 1.5 0 0 1-1.5 1.5H14a2 2 0 1 1-4 0H5a2 2 0 1 1-3.998-.085A1.5 1.5 0 0 1 0 10.5v-7zm1.294 7.456A1.999 1.999 0 0 1 4.732 11h5.536a2.01 2.01 0 0 1 .732-.732V3.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .294.456zM12 10a2 2 0 0 1 1.732 1h.768a.5.5 0 0 0 .5-.5V8.35a.5.5 0 0 0-.11-.312l-1.48-1.85A.5.5 0 0 0 13.02 6H12v4zm-9 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm9 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
                </svg><br>
                Exports
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'import_list' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-truck-front" viewBox="0 0 16 16">
                  <path d="M5 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm8 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm-6-1a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2H7ZM4 2a1 1 0 0 0-1 1v3.9c0 .625.562 1.092 1.17.994C5.075 7.747 6.792 7.5 8 7.5c1.208 0 2.925.247 3.83.394A1.008 1.008 0 0 0 13 6.9V3a1 1 0 0 0-1-1H4Zm0 1h8v3.9c0 .002 0 .001 0 0l-.002.004a.013.013 0 0 1-.005.002h-.004C11.088 6.761 9.299 6.5 8 6.5s-3.088.26-3.99.406h-.003a.013.013 0 0 1-.005-.002L4 6.9c0 .001 0 .002 0 0V3Z"/>
                  <path d="M1 2.5A2.5 2.5 0 0 1 3.5 0h9A2.5 2.5 0 0 1 15 2.5v9c0 .818-.393 1.544-1 2v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5V14H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2a2.496 2.496 0 0 1-1-2v-9ZM3.5 1A1.5 1.5 0 0 0 2 2.5v9A1.5 1.5 0 0 0 3.5 13h9a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 12.5 1h-9Z"/>
                </svg><br>
                Imports
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'user_profile' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-check" viewBox="0 0 16 16">
                  <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Zm1.679-4.493-1.335 2.226a.75.75 0 0 1-1.174.144l-.774-.773a.5.5 0 0 1 .708-.708l.547.548 1.17-1.951a.5.5 0 1 1 .858.514ZM11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"/>
                  <path d="M8.256 14a4.474 4.474 0 0 1-.229-1.004H3c.001-.246.154-.986.832-1.664C4.484 10.68 5.711 10 8 10c.26 0 .507.009.74.025.226-.341.496-.65.804-.918C9.077 9.038 8.564 9 8 9c-5 0-6 3-6 4s1 1 1 1h5.256Z"/>
                </svg><br>
                Profile
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="{% url 'processes' %}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-question" viewBox="0 0 16 16">
                  <path d="M8.05 9.6c.336 0 .504-.24.554-.627.04-.534.198-.815.847-1.26.673-.475 1.049-1.09 1.049-1.986 0-1.325-.92-2.227-2.262-2.227-1.02 0-1.792.492-2.1 1.29A1.71 1.71 0 0 0 6 5.48c0 .393.203.64.545.64.272 0 .455-.147.564-.51.158-.592.525-.915 1.074-.915.61 0 1.03.446 1.03 1.084 0 .563-.208.885-.822 1.325-.619.433-.926.914-.926 1.64v.111c0 .428.208.745.585.745z"/>
                  <path d="m10.273 2.513-.921-.944.715-.698.622.637.89-.011a2.89 2.89 0 0 1 2.924 2.924l-.01.89.636.622a2.89 2.89 0 0 1 0 4.134l-.637.622.011.89a2.89 2.89 0 0 1-2.924 2.924l-.89-.01-.622.636a2.89 2.89 0 0 1-4.134 0l-.622-.637-.89.011a2.89 2.89 0 0 1-2.924-2.924l.01-.89-.636-.622a2.89 2.89 0 0 1 0-4.134l.637-.622-.011-.89a2.89 2.89 0 0 1 2.924-2.924l.89.01.622-.636a2.89 2.89 0 0 1 4.134 0l-.715.698a1.89 1.89 0 0 0-2.704 0l-.92.944-1.32-.016a1.89 1.89 0 0 0-1.911 1.912l.016 1.318-.944.921a1.89 1.89 0 0 0 0 2.704l.944.92-.016 1.32a1.89 1.89 0 0 0 1.912 1.911l1.318-.016.921.944a1.89 1.89 0 0 0 2.704 0l.92-.944 1.32.016a1.89 1.89 0 0 0 1.911-1.912l-.016-1.318.944-.921a1.89 1.89 0 0 0 0-2.704l-.944-.92.016-1.32a1.89 1.89 0 0 0-1.912-1.911l-1.318.016z"/>
                  <path d="M7.001 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0z"/>
                </svg><br>
                How To
              </a>
            </li>
          {% endif %}
        <ul>
      </div>

      {% if messages %}
        {% for message in messages %}
          <div class="container text-center mt-5" style="max-width: 600px;" id="alert">
            {% if message.tags == "warning" %}
            
              <div class="alert alert-{{ message.tags }} alert-dismissible show" role="alert">
                <i class="icon fa fa-exclamation-triangle"></i>
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                  <span aria-hidden="True">&times;</span>
                </button>
              </div>
            {% else %}
              <div class="alert alert-{{ message.tags }} alert-dismissible show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                  <span aria-hidden="True">&times;</span>
                </button>
              </div>
            {% endif %}
          </div>
        {% endfor %}
      {% endif %}

      {% block content %}{% endblock content %}
    </div>
    </div>
	
    <!-- =-=-=-=-=-=-= Quote Modal =-=-=-=-=-=-= -->
    <div data-target="#request-quote" data-toggle="modal" class="quote-button hidden-xs">
      <a class="btn btn-primary" href="{% url 'cargo_booking' %}"><i class="fa fa-envelope"></i></a>
    </div>
    <!-- =-=-=-=-=-=-= Quote Modal End =-=-=-=-=-=-= -->
    

    <!-- JavaScripts -->
    <script src="{% static 'js/modernizr.js' %}"></script>
    <!-- =-=-=-=-=-=-= JQUERY =-=-=-=-=-=-= -->
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/jquery-migrate.min.js' %}"></script>
    <!-- Bootstrap Core Css  -->
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <!-- Dropdown Hover  -->
     <script src="{% static 'js/bootstrap-dropdownhover.min.js' %}"></script><!-- Jquery Easing -->
    <script type="text/javascript" src="{% static 'js/easing.js' %}"></script>
    <!-- Jquery Counter -->
    <script src="{% static 'js/jquery.countTo.js' %}"></script>
    <!-- Jquery Waypoints -->
    <script src="{% static 'js/jquery.waypoints.js' %}"></script>
    <!-- Jquery Appear Plugin -->
    <script src="{% static 'js/jquery.appear.min.js' %}"></script>
    <!-- Jquery Shuffle Portfolio -->
    <script src="{% static 'js/jquery.shuffle.min.js' %}"></script>
    <!-- Carousel Slider  -->
    <script src="{% static 'js/carousel.min.js' %}"></script>
    <!--for online booking page-->
    <script src="{% static 'js/select2.min.js' %}"></script>
    <script src="{% static 'js/moment.js' %}"></script>
    <script src="{% static 'js/bootstrap-datetimepicker.min.js' %}"></script>
    <!--Style Switcher -->
    <script src="{% static 'js/color-switcher.js' %}"></script>
    <!-- Gallery Magnify  -->
    <script src="{% static 'js/magnific-popup/jquery.magnific-popup.min.js' %}"></script>
    <!-- Sticky Bar  -->
    <script src="{% static 'js/theia-sticky-sidebar.js' %}"></script>
    <!-- Template Core JS -->
    <script src="{% static 'js/custom.js' %}"></script>
    <!--htmx script-->
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <!--intlTelINput js-->
    <scrip src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput.min.js"></script>
      
    <script>
      function toggleSidebar() {
        var sidebar = document.querySelector('.sidebar');
        var mainContent = document.querySelector('.main'); 

        sidebar.classList.toggle('collapsed');

        if (sidebar.classList.contains('collapsed')) {
          mainContent.style.width = '100%';
        } else {
          mainContent.style.width = "100%";
        }
      }

       // Alert
      var alert = document.getElementById('alert');
      function hideAlert() {
        alert.style.transition = 'opacity 4s ease'
        alert.style.opacity = '3'
        alert.style.display = 'none';
        setTimeout(function() {
          alert.style.display = 'none';
        }, 1000);
      }
      setTimeout(hideAlert, 4000);
    </script>  
  </body>
</html>