*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.form-control input{
    outline: none !important;
    padding: 0px 10px;
    box-shadow: none !important;
}

a{
    text-decoration: none;
}

body{
    font-family: Verdana, Geneva, Tahoma, sans-serif;
}

.container-fluid{
    height: 100vh !important;
}

.bg-gold{
    background-color: goldenrod;
}

.link{
    text-decoration: none;
    text-transform: capitalize;
}

.link:hover{
    color: aliceblue;
    position: relative;
    left: -0.3rem;
}

/* navbar style*/

.container-fluid .left-sidebar{
    padding-top: 80px !important;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.navbar-img{
    height: 40px;
    width: 40px;
    border-radius: 50px;
}
.nav-link:hover{
    color: gold;
}

.navbar-nav .nav-link:hover, .navbar-brand:hover{
    color: white !important;
}

.navbar-img:hover{
    opacity: 0.7;
    color: black;
}

/* flexbox */
.sidebar-main-container{
    display: flex;
    justify-content: space-between;
}
/* flexbox */


/* left-sidebar style */
.left-sidebar{
    position: fixed;
    top:0;
    left:0;
    bottom: 0;
    z-index: 100;
    overflow-y: auto;
    text-align: center;
    height: 100%;
    width: 150px;
    border-bottom: 1px solid #e1e4ed;
}

.items{
    margin-bottom: 8rem;
    margin: 0;
    padding: 0;
}

.left-sidebar .items .nav-link{
    display: block;
    color: #3a3a3a;
    border-bottom: 2px solid solid #e1e4ed;;
    border-top: 2px solid #e1e4ed;;
    padding: 20px;
    line-height: 20px;
}

.left-sidebar .items .nav-link:hover{
    color: gold;
    border-radius: 5px;
    border-left: 5px solid goldenrod;
}

.nav-item .nav-link svg{
    width: 50px;
    height: 50px;
}
/* end of left-sidebar style */


/* main style*/
main{
    height: 100vh;
    position: absolute;
    right: 0;
    width: calc(100% - 150px);
    padding: 90px 10px 0 10px;
    overflow-x: hidden;
    background-color: #f8f8f8;
    color: #3a3a3a;
}
/* end of main style*/


/* profile detail style */

.profile-picture{
    max-width: 80%;
    height: 300px;
    border: 1px solid white;
    border-radius: 50px;
}

hr{
   margin: 0 !important;
   padding: 5px !important;
}

/* end of profile detail style*/


/* profile update style */

.current-picture{
    max-width: 100%;
    height: 400px;
    border: 1px solid white;
    border-radius: 50px;
}

.profile-update label{
    display: block !important;
    margin-top: 20px !important;
    margin-bottom: -12px !important;
}

/* end profile update style */


/* export detail style */

.sender-detail{
    border-style: dotted !important;
    background-color: rgba(169, 169, 169, 0.288) !important;
}

.sender-detail p{
    margin-bottom: -5px;
}

.receiver-detail{
    border-style: dotted !important;
    background-color: rgba(169, 169, 169, 0.288) !important;
}

.receiver-detail p{
    margin-bottom: -5px;
}

.other-details p{
    margin-top: -10px;
}
/* end export detail style*/