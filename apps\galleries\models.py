import cloudinary
from django.db import models
from django.dispatch import receiver
from django.db.models.signals import pre_delete
from apps.common.models import TimeStampedModel
from cloudinary.models import CloudinaryField
from apps.common.custom_validators import custom_file_validator


class Gallery(TimeStampedModel):
    title = models.CharField(max_length=100)
    picture = CloudinaryField(folder="static/media/images/galleries/", use_filename=True, max_length=500, blank=True, null=True,
                              validators=[custom_file_validator])

    class Meta:
        verbose_name = "Gallery"
        verbose_name_plural = "Galleries"

    def __str__(self):
        return self.title if self.title else "Untitled Gallery"


@receiver(pre_delete, sender=Gallery)
def delete_model_object_and_associated_files(sender, instance, **kwargs):
    if instance.picture:
        cloudinary.uploader.destroy(instance.picture.public_id)
    else:
        pass
