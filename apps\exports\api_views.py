from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db import transaction
from .models import Export, Item
from .serializers import (
    ExportCreateSerializer,
    ExportListSerializer, 
    ExportDetailSerializer,
    ExportTrackingSerializer
)


class ExportCreateAPIView(generics.CreateAPIView):
    """API View to create new export shipment"""
    serializer_class = ExportCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create new export shipment"""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            export_shipment = serializer.save()
            
            # Return created shipment data
            detail_serializer = ExportDetailSerializer(export_shipment)
            
            return Response({
                'success': True,
                'message': 'Export shipment created successfully',
                'data': {
                    'id': str(export_shipment.id),
                    'tracking_no': export_shipment.tracking_no,
                    'export_fee': export_shipment.export_fee,
                    'payment_status': export_shipment.payment_status,
                    'shipping_status': export_shipment.shipping_status
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Export shipment creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class ExportListAPIView(generics.ListAPIView):
    """API View to list user's export shipments"""
    serializer_class = ExportListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get exports for current user"""
        return Export.objects.select_related('customer', 'sender', 'destination').filter(
            customer=self.request.user
        ).order_by('-created_on')
    
    def list(self, request, *args, **kwargs):
        """List user's export shipments with pagination"""
        queryset = self.get_queryset()
        
        # Get pagination parameters
        page = request.GET.get('page', 1)
        limit = min(int(request.GET.get('limit', 20)), 100)  # Max 100 items per page
        
        # Paginate results
        paginator = Paginator(queryset, limit)
        page_obj = paginator.get_page(page)
        
        # Serialize data
        serializer = self.get_serializer(page_obj.object_list, many=True)
        
        return Response({
            'success': True,
            'data': {
                'count': paginator.count,
                'next': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'results': serializer.data
            }
        }, status=status.HTTP_200_OK)


class ExportDetailAPIView(generics.RetrieveAPIView):
    """API View to get detailed export shipment information"""
    serializer_class = ExportDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Get exports for current user"""
        return Export.objects.select_related('customer', 'sender', 'destination').filter(
            customer=self.request.user
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Get detailed export shipment information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class ExportTrackingAPIView(APIView):
    """API View for public export tracking"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def get(self, request, tracking_number):
        """Track export shipment by tracking number"""
        try:
            export_shipment = Export.objects.select_related('sender', 'destination').get(
                tracking_no=tracking_number
            )
            
            serializer = ExportTrackingSerializer(export_shipment)
            
            return Response({
                'success': True,
                'data': {
                    'tracking_no': export_shipment.tracking_no,
                    'type': 'export',
                    'status': export_shipment.shipping_status,
                    'sender': serializer.data['sender'],
                    'destination': serializer.data['destination'],
                    'weight': serializer.data['weight_display'],
                    'shipped_at': export_shipment.shipped_at,
                    'estimated_delivery': None,  # You can add logic for estimated delivery
                    'tracking_history': [
                        {
                            'status': 'Shipment Created',
                            'timestamp': export_shipment.created_on,
                            'location': serializer.data['sender']['address'] if serializer.data['sender'] else 'Origin'
                        },
                        {
                            'status': export_shipment.shipping_status,
                            'timestamp': export_shipment.shipped_at or export_shipment.updated_on,
                            'location': 'In Transit' if export_shipment.shipping_status != 'Pending' else 'Origin'
                        }
                    ]
                }
            }, status=status.HTTP_200_OK)
            
        except Export.DoesNotExist:
            return Response({
                'success': False,
                'message': 'No matching export shipment found for this tracking number'
            }, status=status.HTTP_404_NOT_FOUND)


class ExportStatsAPIView(APIView):
    """API View to get export statistics for user"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get user's export statistics"""
        user = request.user
        
        # Get export statistics
        total_exports = Export.objects.filter(customer=user).count()
        pending_exports = Export.objects.filter(customer=user, shipping_status='Pending').count()
        in_transit_exports = Export.objects.filter(customer=user, shipping_status='In Transit').count()
        delivered_exports = Export.objects.filter(customer=user, shipping_status='Delivered').count()
        unpaid_exports = Export.objects.filter(customer=user, payment_status='Unpaid').count()
        
        return Response({
            'success': True,
            'data': {
                'total_exports': total_exports,
                'pending_exports': pending_exports,
                'in_transit_exports': in_transit_exports,
                'delivered_exports': delivered_exports,
                'unpaid_exports': unpaid_exports
            }
        }, status=status.HTTP_200_OK)
