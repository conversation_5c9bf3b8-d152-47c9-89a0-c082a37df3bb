# Generated by Django 4.2.14 on 2025-06-27 10:10

from django.db import migrations, models
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.CharField(db_index=True, default=uuid.uuid4, editable=False, max_length=64, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ('address', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(choices=[('', 'Select country'), ('Nigeria', 'Nigeria'), ('USA', 'USA'), ('Canada', 'Canada'), ('United kingdom', 'United Kingdom')], max_length=100)),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=14, region=None)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
