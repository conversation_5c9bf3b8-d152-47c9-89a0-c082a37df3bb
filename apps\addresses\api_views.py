from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.core.paginator import Paginator
from .models import Address
from .serializers import (
    AddressSerializer,
    AddressCreateSerializer,
    AddressUpdateSerializer
)


class AddressListCreateAPIView(generics.ListCreateAPIView):
    """API View to list and create addresses"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get addresses for current user"""
        return Address.objects.filter(user=self.request.user).order_by('-created_on')
    
    def get_serializer_class(self):
        """Return appropriate serializer based on request method"""
        if self.request.method == 'POST':
            return AddressCreateSerializer
        return AddressSerializer
    
    def list(self, request, *args, **kwargs):
        """List user's addresses"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    def create(self, request, *args, **kwargs):
        """Create new address"""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            address = serializer.save()
            
            # Return created address data
            response_serializer = AddressSerializer(address)
            
            return Response({
                'success': True,
                'message': 'Address created successfully',
                'data': response_serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Address creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class AddressDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """API View to get, update, and delete specific address"""
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Get addresses for current user"""
        return Address.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on request method"""
        if self.request.method in ['PUT', 'PATCH']:
            return AddressUpdateSerializer
        return AddressSerializer
    
    def retrieve(self, request, *args, **kwargs):
        """Get specific address"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Address not found'
            }, status=status.HTTP_404_NOT_FOUND)
    
    def update(self, request, *args, **kwargs):
        """Update address"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            
            if serializer.is_valid():
                serializer.save()
                
                # Return updated address data
                response_serializer = AddressSerializer(instance)
                
                return Response({
                    'success': True,
                    'message': 'Address updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
            
            return Response({
                'success': False,
                'message': 'Address update failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except:
            return Response({
                'success': False,
                'message': 'Address not found'
            }, status=status.HTTP_404_NOT_FOUND)
    
    def destroy(self, request, *args, **kwargs):
        """Delete address"""
        try:
            instance = self.get_object()
            
            # Check if address is being used in any shipments
            if (hasattr(instance, 'export_sender') and instance.export_sender.exists()) or \
               (hasattr(instance, 'export_destination') and instance.export_destination.exists()) or \
               (hasattr(instance, 'import_sender') and instance.import_sender.exists()) or \
               (hasattr(instance, 'import_destination') and instance.import_destination.exists()) or \
               (hasattr(instance, 'logistic_sender') and instance.logistic_sender.exists()):
                return Response({
                    'success': False,
                    'message': 'Cannot delete address that is being used in shipments'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            instance.delete()
            
            return Response({
                'success': True,
                'message': 'Address deleted successfully'
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Address not found'
            }, status=status.HTTP_404_NOT_FOUND)
