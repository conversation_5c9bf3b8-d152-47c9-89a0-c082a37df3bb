from rest_framework import serializers
from .models import Export, Item
from apps.addresses.models import Address
from apps.users.models import CustomUser


class ExportItemSerializer(serializers.ModelSerializer):
    """Serializer for Export Items"""
    
    class Meta:
        model = Item
        fields = ['id', 'name', 'quantity', 'price', 'total_price', 'weight']
        read_only_fields = ['id', 'total_price']


class ExportCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Export shipments"""
    items = ExportItemSerializer(many=True, write_only=True)
    
    class Meta:
        model = Export
        fields = [
            'sender', 'destination', 'phone_no', 'weight', 'weight_unit',
            'lenght', 'breadth', 'height', 'attention', 'items'
        ]
    
    def validate(self, attrs):
        """Validate export data"""
        items = attrs.get('items', [])
        if not items:
            raise serializers.ValidationError("At least one item is required.")
        
        # Validate that sender and destination exist
        sender_id = attrs.get('sender')
        destination_id = attrs.get('destination')
        
        if sender_id and not Address.objects.filter(id=sender_id.id).exists():
            raise serializers.ValidationError("Invalid sender address.")
        
        if destination_id and not Address.objects.filter(id=destination_id.id).exists():
            raise serializers.ValidationError("Invalid destination address.")
        
        return attrs
    
    def create(self, validated_data):
        """Create export shipment with items"""
        items_data = validated_data.pop('items')
        
        # Create export shipment
        export_shipment = Export.objects.create(
            customer=self.context['request'].user,
            **validated_data
        )
        
        # Create items
        total_export_fee = 0
        for item_data in items_data:
            item = Item.objects.create(
                exports=export_shipment,
                **item_data
            )
            total_export_fee += float(item.total_price.amount)
        
        # Calculate and set export fee (you can customize this logic)
        export_shipment.export_fee = total_export_fee * 0.12  # 12% of total item value
        export_shipment.save()
        
        return export_shipment


class ExportListSerializer(serializers.ModelSerializer):
    """Serializer for listing Export shipments"""
    sender = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    weight_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Export
        fields = [
            'id', 'tracking_no', 'sender', 'destination', 'weight_display',
            'export_fee', 'payment_status', 'shipping_status', 'created_on'
        ]
    
    def get_sender(self, obj):
        """Get sender address info"""
        if obj.sender:
            return {
                'name': obj.sender.name,
                'address': obj.sender.get_short_address()
            }
        return None
    
    def get_destination(self, obj):
        """Get destination address info"""
        if obj.destination:
            return {
                'name': obj.destination.name,
                'address': obj.destination.get_short_address()
            }
        return None
    
    def get_weight_display(self, obj):
        """Get formatted weight"""
        return obj.get_weight


class ExportDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed Export shipment view"""
    customer = serializers.SerializerMethodField()
    sender = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    items = ExportItemSerializer(source='item_set', many=True, read_only=True)
    dimensions = serializers.SerializerMethodField()
    
    class Meta:
        model = Export
        fields = [
            'id', 'tracking_no', 'customer', 'sender', 'destination',
            'phone_no', 'weight', 'weight_unit', 'dimensions',
            'export_fee', 'payment_status', 'payment_reference',
            'shipping_status', 'shipped_at', 'attention', 'items',
            'created_on', 'updated_on'
        ]
    
    def get_customer(self, obj):
        """Get customer info"""
        return {
            'id': str(obj.customer.id),
            'name': f"{obj.customer.first_name} {obj.customer.last_name}",
            'email': obj.customer.email
        }
    
    def get_sender(self, obj):
        """Get detailed sender address"""
        if obj.sender:
            return {
                'id': str(obj.sender.id),
                'name': obj.sender.name,
                'address': obj.sender.address,
                'city': obj.sender.city,
                'state': obj.sender.state,
                'country': obj.sender.country,
                'phone_number': str(obj.sender.phone_number)
            }
        return None
    
    def get_destination(self, obj):
        """Get detailed destination address"""
        if obj.destination:
            return {
                'id': str(obj.destination.id),
                'name': obj.destination.name,
                'address': obj.destination.address,
                'city': obj.destination.city,
                'state': obj.destination.state,
                'country': obj.destination.country,
                'phone_number': str(obj.destination.phone_number)
            }
        return None
    
    def get_dimensions(self, obj):
        """Get shipment dimensions"""
        return {
            'length': obj.lenght,
            'breadth': obj.breadth,
            'height': obj.height
        }


class ExportTrackingSerializer(serializers.ModelSerializer):
    """Serializer for public export tracking"""
    sender = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    weight_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Export
        fields = [
            'tracking_no', 'sender', 'destination', 'weight_display',
            'shipping_status', 'shipped_at', 'created_on'
        ]
    
    def get_sender(self, obj):
        """Get sender location for tracking"""
        if obj.sender:
            return {
                'name': obj.sender.name,
                'address': obj.sender.get_short_address()
            }
        return None
    
    def get_destination(self, obj):
        """Get destination location for tracking"""
        if obj.destination:
            return {
                'name': obj.destination.name,
                'address': obj.destination.get_short_address()
            }
        return None
    
    def get_weight_display(self, obj):
        """Get formatted weight for tracking"""
        return obj.get_weight
