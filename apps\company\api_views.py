from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db import transaction
from decimal import Decimal
from .models import Company
from apps.logistics.models import Logistic
from .serializers import (
    CompanyRegistrationSerializer,
    CompanySerializer,
    CompanyUpdateSerializer,
    CompanyStatsSerializer
)


class CompanyRegistrationAPIView(generics.CreateAPIView):
    """API View for company registration"""
    serializer_class = CompanyRegistrationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Register a new company"""
        # Check if user already has a company
        if Company.objects.filter(user=request.user).exists():
            return Response({
                'success': False,
                'message': 'You already have a registered company'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            company = serializer.save()
            
            # Update user type to small business
            user = request.user
            user.user_type = "small business"
            user.save()
            
            # Return created company data
            response_serializer = CompanySerializer(company)
            
            return Response({
                'success': True,
                'message': 'Company registered successfully',
                'data': response_serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Company registration failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class CompanyDetailAPIView(generics.RetrieveUpdateAPIView):
    """API View for company profile management"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Get current user's company"""
        try:
            return Company.objects.get(user=self.request.user)
        except Company.DoesNotExist:
            return None
    
    def get_serializer_class(self):
        """Return appropriate serializer based on request method"""
        if self.request.method in ['PUT', 'PATCH']:
            return CompanyUpdateSerializer
        return CompanySerializer
    
    def retrieve(self, request, *args, **kwargs):
        """Get company profile"""
        instance = self.get_object()
        
        if not instance:
            return Response({
                'success': False,
                'message': 'No company found for this user'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = self.get_serializer(instance)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    def update(self, request, *args, **kwargs):
        """Update company profile"""
        instance = self.get_object()
        
        if not instance:
            return Response({
                'success': False,
                'message': 'No company found for this user'
            }, status=status.HTTP_404_NOT_FOUND)
        
        partial = kwargs.pop('partial', False)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            
            # Return updated company data
            response_serializer = CompanySerializer(instance)
            
            return Response({
                'success': True,
                'message': 'Company profile updated successfully',
                'data': response_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': 'Company profile update failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class CompanyListAPIView(generics.ListAPIView):
    """API View to list user's companies (for users who might have multiple companies)"""
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get companies for current user"""
        return Company.objects.filter(user=self.request.user)
    
    def list(self, request, *args, **kwargs):
        """List user's companies"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class CompanyStatsAPIView(APIView):
    """API View for company statistics and dashboard"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get company statistics"""
        try:
            company = Company.objects.get(user=request.user)
        except Company.DoesNotExist:
            return Response({
                'success': False,
                'message': 'No company found for this user'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get logistics statistics
        total_logistics_orders = Logistic.objects.filter(company=company).count()
        pending_orders = Logistic.objects.filter(company=company, delivery_status='Pending').count()
        in_transit_orders = Logistic.objects.filter(company=company, delivery_status='In Transit').count()
        delivered_orders = Logistic.objects.filter(company=company, delivery_status='Delivered').count()
        unpaid_orders = Logistic.objects.filter(company=company, payment_status='Unpaid').count()
        
        # Calculate total revenue (from paid orders)
        paid_orders = Logistic.objects.filter(company=company, payment_status='Paid')
        total_revenue = sum([float(order.delivery_fee.amount) for order in paid_orders])
        
        # Get recent orders (last 5)
        recent_orders = Logistic.objects.filter(company=company).order_by('-created_on')[:5]
        recent_orders_data = []
        
        for order in recent_orders:
            recent_orders_data.append({
                'id': str(order.id),
                'tracking_no': order.tracking_no,
                'receiver_name': order.receiver_name,
                'delivery_status': order.delivery_status,
                'payment_status': order.payment_status,
                'delivery_fee': float(order.delivery_fee.amount),
                'created_on': order.created_on
            })
        
        stats_data = {
            'total_logistics_orders': total_logistics_orders,
            'pending_orders': pending_orders,
            'in_transit_orders': in_transit_orders,
            'delivered_orders': delivered_orders,
            'unpaid_orders': unpaid_orders,
            'total_revenue': total_revenue,
            'recent_orders': recent_orders_data
        }
        
        serializer = CompanyStatsSerializer(stats_data)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class CompanyVerificationStatusAPIView(APIView):
    """API View to check company verification status"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Check if user has a verified company"""
        try:
            company = Company.objects.get(user=request.user)
            
            return Response({
                'success': True,
                'data': {
                    'has_company': True,
                    'company_id': str(company.id),
                    'business_name': company.business_name,
                    'rc_number': company.rc_number,
                    'is_verified': True  # You can add verification logic here
                }
            }, status=status.HTTP_200_OK)
            
        except Company.DoesNotExist:
            return Response({
                'success': True,
                'data': {
                    'has_company': False,
                    'company_id': None,
                    'business_name': None,
                    'rc_number': None,
                    'is_verified': False
                }
            }, status=status.HTTP_200_OK)
