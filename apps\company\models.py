import cloudinary
from django.db import models
from django.dispatch import receiver
from apps.users.models import CustomUser
from django.db.models.signals import pre_delete
from apps.common.models import TimeStampedModel
from apps.common.custom_validators import custom_file_validator
from django.core.validators import <PERSON><PERSON><PERSON>thValida<PERSON>, MaxLengthValidator
from cloudinary.models import CloudinaryField


class Company(TimeStampedModel):
    user = models.ForeignKey(CustomUser, related_name="company", on_delete=models.CASCADE)
    business_name = models.CharField(max_length=150, unique=True)
    rc_number = models.CharField(validators=[MinLengthValidator(7), MaxLengthValidator(7)], unique=True)
    company_address = models.TextField()
    cac_certificate = CloudinaryField(folder="static/media/images/company_documents/", blank=True, null=True, use_filename=True,
                                      max_length=500, validators=[custom_file_validator])

    class Meta:
        verbose_name = "Business"
        verbose_name_plural = "Businesses"
        ordering = ("-created_on",)
        unique_together = ("user", "business_name")

    def __str__(self):
        return self.business_name


@receiver(pre_delete, sender=Company)
def delete_model_object_and_associated_files(sender, instance, **kwargs):
    if instance.cac_certificate:
        cloudinary.uploader.destroy(instance.cac_certificate.public_id)
    else:
        pass
