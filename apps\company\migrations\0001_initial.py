# Generated by Django 4.2.14 on 2025-07-15 19:12

import apps.common.custom_validators
import cloudinary.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.CharField(db_index=True, default=uuid.uuid4, editable=False, max_length=64, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('business_name', models.CharField(max_length=150, unique=True)),
                ('rc_number', models.CharField(unique=True, validators=[django.core.validators.MinLengthValidator(7), django.core.validators.MaxLengthValidator(7)])),
                ('company_address', models.TextField()),
                ('cac_certificate', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, validators=[apps.common.custom_validators.custom_file_validator])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
                'ordering': ('-created_on',),
                'unique_together': {('user', 'business_name')},
            },
        ),
    ]
