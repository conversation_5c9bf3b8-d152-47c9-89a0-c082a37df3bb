# MOG Dynamics API Documentation for Flutter Integration

## Overview
This Django application can be used as an API backend for a Flutter mobile app. The current web-based views can be converted to API endpoints that return JSON responses instead of HTML templates.

## Recommended API Architecture

### API Framework Integration
To convert this to a proper REST API, add these dependencies:
```python
# Add to requirements/base.txt
djangorestframework==3.14.0
djangorestframework-simplejwt==5.2.2
django-cors-headers==4.0.0
```

### Base API Structure
```
/api/v1/
├── auth/           # Authentication endpoints
├── users/          # User management
├── companies/      # Company operations
├── exports/        # Export shipments
├── imports/        # Import shipments
├── logistics/      # Local logistics
├── addresses/      # Address management
├── pricing/        # Pricing information
├── galleries/      # Image galleries
└── tracking/       # Shipment tracking
```

## API Endpoints Documentation

### 1. Authentication Endpoints (`/api/v1/auth/`)

#### Register User
```http
POST /api/v1/auth/register/
Content-Type: multipart/form-data

{
  "first_name": "string",
  "last_name": "string", 
  "email": "string",
  "password": "string",
  "confirm_password": "string",
  "gender": "male|female",
  "phone_no": "string",
  "address": "string",
  "city": "string",
  "state": "string", 
  "country": "string",
  "document_type": "string",
  "document": "file",
  "profile_picture": "file (optional)"
}

Response (201):
{
  "success": true,
  "message": "Account created successfully. Check email for activation link.",
  "data": {
    "user_id": "uuid",
    "email": "string",
    "profile_code": "string"
  }
}
```

#### Login
```http
POST /api/v1/auth/login/
Content-Type: application/json

{
  "email": "string",
  "password": "string"
}

Response (200):
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "jwt_token",
    "refresh_token": "jwt_token", 
    "user": {
      "id": "uuid",
      "email": "string",
      "first_name": "string",
      "last_name": "string",
      "user_type": "regular|dispatcher|small business",
      "profile_picture": "url",
      "is_active": boolean
    }
  }
}
```

#### Refresh Token
```http
POST /api/v1/auth/refresh/
Content-Type: application/json

{
  "refresh": "jwt_refresh_token"
}

Response (200):
{
  "access": "new_jwt_token"
}
```

#### Account Activation
```http
POST /api/v1/auth/activate/
Content-Type: application/json

{
  "uidb64": "string",
  "token": "string"
}

Response (200):
{
  "success": true,
  "message": "Account activated successfully"
}
```

### 2. User Management (`/api/v1/users/`)

#### Get User Profile
```http
GET /api/v1/users/profile/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": {
    "id": "uuid",
    "profile_code": "string",
    "first_name": "string",
    "last_name": "string", 
    "email": "string",
    "gender": "string",
    "phone_no": "string",
    "address": "string",
    "city": "string",
    "state": "string",
    "country": "string",
    "document_type": "string",
    "document": "url",
    "profile_picture": "url",
    "user_type": "string",
    "created_on": "datetime",
    "updated_on": "datetime"
  }
}
```

#### Update User Profile
```http
PUT /api/v1/users/profile/
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

{
  "first_name": "string",
  "last_name": "string",
  "phone_no": "string", 
  "address": "string",
  "city": "string",
  "state": "string",
  "profile_picture": "file (optional)"
}

Response (200):
{
  "success": true,
  "message": "Profile updated successfully",
  "data": { /* updated user object */ }
}
```

#### Get User Dashboard Stats
```http
GET /api/v1/users/dashboard/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": {
    "total_exports": integer,
    "total_imports": integer, 
    "total_logistics": integer,
    "pending_payments": integer,
    "recent_shipments": [
      {
        "id": "uuid",
        "type": "export|import|logistics",
        "tracking_no": "string",
        "status": "string",
        "created_on": "datetime"
      }
    ]
  }
}
```

### 3. Company Management (`/api/v1/companies/`)

#### Register Company
```http
POST /api/v1/companies/register/
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

{
  "business_name": "string",
  "rc_number": "string (7 chars)",
  "company_address": "string",
  "cac_certificate": "file"
}

Response (201):
{
  "success": true,
  "message": "Company registered successfully",
  "data": {
    "id": "uuid",
    "business_name": "string",
    "rc_number": "string",
    "company_address": "string",
    "cac_certificate": "url"
  }
}
```

#### Get User Companies
```http
GET /api/v1/companies/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "business_name": "string",
      "rc_number": "string", 
      "company_address": "string",
      "cac_certificate": "url",
      "created_on": "datetime"
    }
  ]
}
```

### 4. Export Shipments (`/api/v1/exports/`)

#### Create Export Shipment
```http
POST /api/v1/exports/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "sender_id": "uuid",
  "destination_id": "uuid",
  "phone_no": "string",
  "weight": decimal,
  "weight_unit": "Kg|Mts|Ton|Lbs",
  "length": float,
  "breadth": float,
  "height": float,
  "attention": "string",
  "items": [
    {
      "name": "string",
      "quantity": integer,
      "price": decimal,
      "weight": float
    }
  ]
}

Response (201):
{
  "success": true,
  "message": "Export shipment created successfully",
  "data": {
    "id": "uuid",
    "tracking_no": "string",
    "export_fee": decimal,
    "payment_status": "Unpaid",
    "shipping_status": "Pending"
  }
}
```

#### Get Export List
```http
GET /api/v1/exports/?page=1&limit=10
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": {
    "count": integer,
    "next": "url|null",
    "previous": "url|null", 
    "results": [
      {
        "id": "uuid",
        "tracking_no": "string",
        "sender": {
          "name": "string",
          "address": "string"
        },
        "destination": {
          "name": "string", 
          "address": "string"
        },
        "weight": "string",
        "export_fee": decimal,
        "payment_status": "string",
        "shipping_status": "string",
        "created_on": "datetime"
      }
    ]
  }
}
```

#### Get Export Details
```http
GET /api/v1/exports/{id}/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": {
    "id": "uuid",
    "tracking_no": "string",
    "customer": { /* user object */ },
    "sender": { /* address object */ },
    "destination": { /* address object */ },
    "phone_no": "string",
    "weight": decimal,
    "weight_unit": "string",
    "dimensions": {
      "length": float,
      "breadth": float,
      "height": float
    },
    "export_fee": decimal,
    "payment_status": "string",
    "payment_reference": "string",
    "shipping_status": "string",
    "shipped_at": "datetime|null",
    "attention": "string",
    "items": [
      {
        "id": "uuid",
        "name": "string",
        "quantity": integer,
        "price": decimal,
        "total_price": decimal,
        "weight": float
      }
    ],
    "created_on": "datetime",
    "updated_on": "datetime"
  }
}
```

### 5. Import Shipments (`/api/v1/imports/`)

#### Create Import Shipment
```http
POST /api/v1/imports/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "sender_id": "uuid",
  "destination_id": "uuid",
  "phone_no": "string",
  "weight": decimal,
  "weight_unit": "string",
  "length": float,
  "breadth": float,
  "height": float,
  "attention": "string",
  "items": [
    {
      "name": "string",
      "quantity": integer,
      "price": decimal,
      "weight": float
    }
  ]
}

Response (201):
{
  "success": true,
  "message": "Import shipment created successfully",
  "data": {
    "id": "uuid",
    "tracking_no": "string",
    "import_fee": decimal,
    "payment_status": "Unpaid",
    "shipping_status": "Pending"
  }
}
```

#### Get Import List & Details
Similar structure to exports, but with `import_fee` instead of `export_fee`

### 6. Local Logistics (`/api/v1/logistics/`)

#### Create Logistics Order (Company Users)
```http
POST /api/v1/logistics/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "sender_id": "uuid",
  "receiver_name": "string",
  "receiver_address": "string",
  "receiver_city": "string",
  "receiver_state": "string",
  "phone_no": "string",
  "weight": decimal,
  "weight_unit": "string",
  "dimensions": {
    "length": float,
    "breadth": float,
    "height": float
  },
  "attention": "string",
  "items": [
    {
      "name": "string",
      "quantity": integer,
      "price": decimal,
      "weight": float
    }
  ]
}

Response (201):
{
  "success": true,
  "message": "Logistics order created successfully",
  "data": {
    "id": "uuid",
    "tracking_no": "string",
    "delivery_fee": decimal,
    "payment_status": "Unpaid",
    "delivery_status": "Pending"
  }
}
```

#### Get Company Orders
```http
GET /api/v1/logistics/orders/?page=1&limit=10
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": {
    "count": integer,
    "results": [
      {
        "id": "uuid",
        "tracking_no": "string",
        "receiver_name": "string",
        "receiver_address": "string",
        "delivery_fee": decimal,
        "payment_status": "string",
        "delivery_status": "string",
        "dispatcher": {
          "name": "string|null"
        },
        "created_on": "datetime"
      }
    ]
  }
}
```

#### Get Dispatcher Assignments (Dispatcher Users)
```http
GET /api/v1/logistics/assignments/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "tracking_no": "string",
      "company": {
        "business_name": "string"
      },
      "receiver_name": "string",
      "receiver_address": "string",
      "delivery_status": "string",
      "created_on": "datetime"
    }
  ]
}
```

#### Update Delivery Status (Dispatcher)
```http
PUT /api/v1/logistics/{id}/status/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "delivery_status": "Pending|In Transit|Delivered|Cancelled"
}

Response (200):
{
  "success": true,
  "message": "Delivery status updated successfully"
}
```

### 7. Address Management (`/api/v1/addresses/`)

#### Create Address
```http
POST /api/v1/addresses/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "string",
  "address": "string",
  "city": "string",
  "state": "string",
  "country": "Nigeria|USA|Canada|United Kingdom",
  "phone_number": "string"
}

Response (201):
{
  "success": true,
  "message": "Address created successfully",
  "data": {
    "id": "uuid",
    "name": "string",
    "address": "string",
    "city": "string",
    "state": "string",
    "country": "string",
    "phone_number": "string"
  }
}
```

#### Get User Addresses
```http
GET /api/v1/addresses/
Authorization: Bearer {access_token}

Response (200):
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "address": "string",
      "city": "string",
      "state": "string",
      "country": "string",
      "phone_number": "string",
      "created_on": "datetime"
    }
  ]
}
```

### 8. Payment Processing (`/api/v1/payments/`)

#### Initiate Payment
```http
POST /api/v1/payments/initiate/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "shipment_type": "export|import|logistics",
  "shipment_id": "uuid"
}

Response (200):
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "authorization_url": "string",
    "access_code": "string",
    "reference": "string"
  }
}
```

#### Verify Payment
```http
POST /api/v1/payments/verify/
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "reference": "string",
  "shipment_type": "export|import|logistics"
}

Response (200):
{
  "success": true,
  "message": "Payment verified successfully",
  "data": {
    "status": "success|failed",
    "amount": decimal,
    "reference": "string",
    "shipment_id": "uuid"
  }
}
```

### 9. Shipment Tracking (`/api/v1/tracking/`)

#### Track Shipment
```http
GET /api/v1/tracking/{tracking_number}/
# No authentication required for public tracking

Response (200):
{
  "success": true,
  "data": {
    "tracking_no": "string",
    "type": "export|import|logistics",
    "status": "string",
    "sender": {
      "name": "string",
      "address": "string"
    },
    "destination": {
      "name": "string",
      "address": "string"
    },
    "weight": "string",
    "shipped_at": "datetime|null",
    "estimated_delivery": "datetime|null",
    "tracking_history": [
      {
        "status": "string",
        "timestamp": "datetime",
        "location": "string"
      }
    ]
  }
}
```

### 10. Static Data (`/api/v1/static/`)

#### Get Pricing Information
```http
GET /api/v1/static/pricing/

Response (200):
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "string",
      "image": "url"
    }
  ]
}
```

#### Get Gallery Images
```http
GET /api/v1/static/galleries/

Response (200):
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "string",
      "picture": "url"
    }
  ]
}
```

#### Get App Configuration
```http
GET /api/v1/static/config/

Response (200):
{
  "success": true,
  "data": {
    "countries": [
      {"code": "NG", "name": "Nigeria"},
      {"code": "US", "name": "United States"}
    ],
    "weight_units": [
      {"code": "kg", "name": "Kg"},
      {"code": "lbs", "name": "Lbs"}
    ],
    "document_types": [
      {"code": "passport", "name": "International Passport"},
      {"code": "drivers_license", "name": "Driver's License"}
    ],
    "user_types": [
      {"code": "regular", "name": "Regular"},
      {"code": "small business", "name": "Small Business"}
    ]
  }
}
```

## Error Response Format

All API endpoints follow a consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Error message for this field"]
  },
  "error_code": "ERROR_CODE"
}
```

Common HTTP Status Codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Authentication

The API uses JWT (JSON Web Tokens) for authentication:

1. **Access Token**: Short-lived (15 minutes), used for API requests
2. **Refresh Token**: Long-lived (7 days), used to get new access tokens

Include the access token in the Authorization header:
```
Authorization: Bearer {access_token}
```

## File Upload Handling

For endpoints that accept file uploads:
- Use `multipart/form-data` content type
- Maximum file size: 5MB
- Supported formats: PNG, JPEG, JPG, PDF
- Files are stored in Cloudinary and URLs are returned

## Pagination

List endpoints support pagination:
```http
GET /api/v1/exports/?page=2&limit=20
```

Response includes pagination metadata:
```json
{
  "count": 150,
  "next": "http://api.example.com/api/v1/exports/?page=3&limit=20",
  "previous": "http://api.example.com/api/v1/exports/?page=1&limit=20",
  "results": [...]
}
```

## Implementation Steps

To convert the existing Django app to support this API:

1. **Install DRF and dependencies**
   ```bash
   pip install djangorestframework djangorestframework-simplejwt django-cors-headers
   ```

2. **Update settings.py**
   ```python
   # Add to INSTALLED_APPS in core/settings/base.py
   INSTALLED_APPS += [
       'rest_framework',
       'rest_framework_simplejwt',
       'corsheaders',
   ]

   # Add CORS middleware
   MIDDLEWARE = [
       'corsheaders.middleware.CorsMiddleware',
       'django.middleware.common.CommonMiddleware',
       # ... other middleware
   ]

   # DRF Configuration
   REST_FRAMEWORK = {
       'DEFAULT_AUTHENTICATION_CLASSES': (
           'rest_framework_simplejwt.authentication.JWTAuthentication',
       ),
       'DEFAULT_PERMISSION_CLASSES': [
           'rest_framework.permissions.IsAuthenticated',
       ],
       'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
       'PAGE_SIZE': 20
   }

   # JWT Configuration
   from datetime import timedelta
   SIMPLE_JWT = {
       'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
       'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
       'ROTATE_REFRESH_TOKENS': True,
   }

   # CORS Configuration
   CORS_ALLOWED_ORIGINS = [
       "http://localhost:3000",  # Flutter web
       "http://127.0.0.1:3000",
   ]
   CORS_ALLOW_ALL_ORIGINS = True  # For development only
   ```

3. **Create API serializers for all models**
4. **Convert views to API views using DRF ViewSets**
5. **Add JWT authentication endpoints**
6. **Update URL patterns for API routes**
7. **Add comprehensive error handling**
8. **Implement proper permissions**
9. **Add API documentation with Swagger**
10. **Test all endpoints**

## Flutter Integration Benefits

- **Consistent Data Format**: All endpoints return standardized JSON
- **Authentication**: JWT tokens for secure mobile sessions
- **File Handling**: Cloudinary integration for images/documents
- **Real-time Updates**: WebSocket support can be added later
- **Offline Capability**: API structure supports caching strategies

## Next Steps

1. **Review the API documentation** and prioritize endpoints
2. **Start with authentication endpoints** as they're foundational
3. **Implement core business logic** (shipments, payments, tracking)
4. **Add real-time features** as needed
5. **Test with Flutter app** integration

This API structure provides everything needed for a full-featured logistics mobile app with user management, shipment creation/tracking, payment processing, and business operations management.
