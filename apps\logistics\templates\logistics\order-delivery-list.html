{% extends 'dashboard.html' %}


{% block content %}
    <!-- =-=-=-=-=-=-= Tracking History =-=-=-=-=-=-= -->
    <section id="order-tracking" class="main container section-padding-80">
        <h3 class="color-black">My deliveries</h3>
        <p>
            <a class="link" href="{% url 'local_order_delivery_list' %}">Delivery</a></h1> |
            view
        </p>
        <hr>
        {% if delivery_list %}
            <div class="">
                <!-- Row -->
                <div class="row">
                    <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12 p-2">
                        <!-- Search Filter -->
                        <div class="tracking-search">
                        <div class="input-group" id="adv-search">
                            <input type="text" class="form-control" placeholder="Track Your Shipment" />
                            <div class="input-group-btn">
                                <div class="btn-group" role="group">
                                    <div class="dropdown dropdown-lg">
                                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><span class="caret"></span></button>
                                        <div class="dropdown-menu dropdown-menu-right" role="menu">
                                            <form class="form-horizontal" role="form">
                                            <div class="form-group">
                                                <label for="filter">Filter by</label>
                                                <select class="form-control">
                                                    <option value="0" selected>Order History</option>
                                                    <option value="delivered">Delivered</option>
                                                    <option value="shipped">Shipped</option>
                                                    <option value="pending">Pending</option>
                                                    <option value="cancelled">Canceled</option>
                                                </select>
                                            </div>
                                            </form>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Search Filter End -->

            <!-- Tracking History -->
            <div class="block-content">
                <div class="table-responsive">
                    <table class="table table-clean-paddings margin-bottom-0">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Sender</th>
                                <th>Delivered From</th>
                                <th>Order Id</th>
                                <th>Destination</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for delivery in delivery_list %}
                                <tr>
                                    <td>
                                        <div class="contact-container">
                                        <div>{{ delivery.company.business_name|capfirst }}</div> 
                                        <span>on {{ delivery.created_on|date }}</span></div>
                                    </td>
                                    <td>{{ delivery.company.business_name }}</td>
                                    <td>{{ delivery.sender.name }}</td>
                                    <td>{{ delivery.tracking_no }}</td>
                                    <td>{{ delivery.receiver_address }}</td>
                                    {% if delivery.delivery_status == "delivered" %}
                                        <td><span class="label label-success label-transparent">{{ delivery.delivery_status|capfirst }}</span></td>
                                    {% elif delivery.delivery_status == "pending" %}
                                        <td><span class="label label-black label-transparent">{{ delivery.delivery_status|capfirst }}</span></td>
                                    {% elif delivery.delivery_status == "shipped" %}
                                        <td><span class="label label-warning label-transparent">{{ delivery.delivery_status|capfirst }}</span></td>
                                    {% elif delivery.delivery_status == "cancelled" %}
                                        <td><span class="label label-danger label-transparent">{{ delivery.delivery_status|capfirst }}</span></td>
                                    {% elif delivery.delivery_status == "in transit" %}
                                        <td><span class="label label-success label-transparent">{{ delivery.delivery_status }}</span></td>
                                    {% endif %}
                                    <td><a href="{% url 'local_order_delivery_update' delivery.id %}">Update Order</a></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <h1 class="text-center">You have no delivery(ies) assigned to you.</h1>
        {% endif %}
    </section>
    <!-- =-=-=-=-=-=-= Tracking History End =-=-=-=-=-=-= -->
{% endblock content %}