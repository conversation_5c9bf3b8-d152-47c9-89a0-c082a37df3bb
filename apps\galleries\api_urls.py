from django.urls import path
from .api_views import (
    GalleryListAPIView,
    GalleryDetailAPIView,
    GalleryCreateAPIView,
    GalleryUpdateAPIView,
    GalleryDeleteAPIView
)

urlpatterns = [
    # Public gallery endpoints
    path('static/galleries/', GalleryListAPIView.as_view(), name='api_gallery_list'),
    path('static/galleries/<str:id>/', GalleryDetailAPIView.as_view(), name='api_gallery_detail'),
    
    # Admin gallery management endpoints
    path('admin/galleries/', GalleryCreateAPIView.as_view(), name='api_gallery_create'),
    path('admin/galleries/<str:id>/', GalleryUpdateAPIView.as_view(), name='api_gallery_update'),
    path('admin/galleries/<str:id>/delete/', GalleryDeleteAPIView.as_view(), name='api_gallery_delete'),
]
