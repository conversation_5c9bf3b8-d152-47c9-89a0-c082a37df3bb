from .base import *

env = environ.Env()
environ.Env.read_env('.env')

DEBUG = True

ALLOWED_HOSTS += ['127.0.0.1', 'www.mogdynamics.com', 'mogdynamics.com', '*************']

CSRF_TRUSTED_ORIGINS = [
    'https://mogdynamics.com',
    'https://www.mogdynamics.com'
]

# Database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        # "ENGINE": "django.db.backends.sqlite3",
        # "NAME": BASE_DIR / "db.sqlite3",
        # "NAME": os.environ.get("DATABASE_NAME"),
        # "USER": os.environ.get("DATABASE_USER"),
        # "PASSWORD": os.environ.get("DATABASE_PASSWORD"),
        # "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        # "PORT": os.environ.get("DATABASE_PORT")
        "NAME": "mdb",  # Your local database name 
        "USER": "postgres",
        "PASSWORD": "fosapostgre",
        "HOST": "localhost",
        "PORT": "5432"
    }
}


# Logging for development

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'my_log_handler': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/debug.log'),
        },
    },
    # 'loggers': {
    #     'django': {
    #         'handlers': ['my_log_handler'],
    #         'level': 'DEBUG' if DEBUG else 'INFO',
    #         'propagate': True,
    #     },
    # },
}
