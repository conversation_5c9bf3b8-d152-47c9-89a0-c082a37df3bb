from django.contrib import messages
from apps.common.custom_validators import convert_to_megabyte
from .models import Company, CustomUser
from django.shortcuts import render, redirect
from core.settings.base import FILE_UPLOAD_MAX_MEMORY_SIZE
from django.core.files.storage import FileSystemStorage
import mimetypes
from django.db import transaction
from apps.users.token import account_activation_token
from django.utils.html import strip_tags
from django.template.loader import render_to_string
from django.contrib.sites.shortcuts import get_current_site
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from core.settings import base
from core import tasks


@transaction.atomic
def register_as_small_business(request):

    template_name = "company/register_as_small_business.html"

    file_types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"]

    if request.method == "POST" and request.FILES:
        first_name = request.POST.get("first-name", None)
        last_name = request.POST.get("last-name")
        gender = request.POST.get("gender", None)
        phone_no = request.POST.get("phone-no", None)
        address = request.POST.get("address", None)
        city = request.POST.get("city", None)
        state = request.POST.get("state", None)
        country = request.POST.get("country", None)
        document_type = request.POST.get("document-type", None)
        document = request.FILES.get("document", None)
        email = request.POST.get("email", None)
        password = request.POST.get("password", None)
        confirm_password = request.POST.get("confirm-password", None)

        # Collects company data
        business_name = request.POST.get("business-name", None)
        rc_number = request.POST.get("rc-number", None)
        company_address = request.POST.get("company-address", None)
        cac_certificate = request.FILES.get("cac-certificate", None)

        if CustomUser.objects.filter(email=email).exists():
            messages.info(request, f"Email already exist.")
            return redirect("small_business_registration")

        if password != confirm_password:
            messages.info(request, f"Passwords do not match!")
            return redirect("small_business_registration")

        if not document or not cac_certificate:
            messages.warning(request, f"Please upload your identification document.")
            return redirect("small_business_registration")

        if document.size > FILE_UPLOAD_MAX_MEMORY_SIZE:
            messages.warning(
                request, f"Document cannot be larger than {convert_to_megabyte(FILE_UPLOAD_MAX_MEMORY_SIZE)}MB.")
            return redirect("small_business_registration")

        if cac_certificate.size > FILE_UPLOAD_MAX_MEMORY_SIZE:
            messages.warning(
                request, f"Cac certificate cannot be larger than {convert_to_megabyte(FILE_UPLOAD_MAX_MEMORY_SIZE)}MB.")
            return redirect("small_business_registration")

        fs = FileSystemStorage()

        document_filename = fs.save(document.name, document)
        document_file_type = mimetypes.guess_type(document_filename)[0]

        certificate_filename = fs.save(cac_certificate.name, cac_certificate)
        certificate_file_type = mimetypes.guess_type(certificate_filename)[0]

        if document_file_type not in file_types:
            messages.info(request, "Invalid document type uploaded, please upload an image.")
            return redirect('small_business_registration')
        
        if certificate_file_type not in file_types:
            messages.info(request, "Invalid cac certificate file uploaded, please upload an image.")
            return redirect('small_business_registration')

        user = CustomUser.objects.create_user(
            first_name=first_name,
            last_name=last_name,
            email=email,
            gender=gender,
            phone_no=phone_no,
            address=address,
            city=city,
            state=state,
            country=country,
            user_type='small business',
            password=password,
            document_type=document_type,
            document=document,
            is_active=False
        )

        if Company.objects.filter(user=user, business_name=business_name, rc_number=rc_number).exists():
            messages.warning(request, f'{business_name} is already registered to a user')
            return redirect("small_business_registration")

        company_obj = Company.objects.create(
            user=user,
            business_name=business_name,
            rc_number=rc_number,
            company_address=company_address,
            cac_certificate=cac_certificate
        )

        context = {
            'user': user,
            'domain': get_current_site(request).domain,
            'uid': urlsafe_base64_encode(force_bytes(user.id)),
            'token': account_activation_token.make_token(user),
        }
        html_message = render_to_string('users/activate_account.html', context)
        message = strip_tags(html_message)
        tasks.send_account_activation_mail_task.delay(
            subject="Activate your Account.",
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL
        )

        messages.success(
            request, f'Account with {user.email} and business name {company_obj.business_name} have been created, kindly check your spam or email for activation link.')
        return redirect("user_login")
    return render(request, template_name)
