from django.core.mail import EmailMessage, send_mail


# Maling class.
class SendEmailClass():
    def __init__(self, email_subject, email_body, sender_email, receiver_email, reply_to: list):
        self.email_subject = email_subject
        self.email_body = email_body
        self.sender_email = sender_email
        self.receiver_email = receiver_email
        self.reply_to = reply_to

    def account_activation_mail(self):
        try:
            msg = EmailMessage(
                subject=self.email_subject,
                body=self.email_body,
                from_email=self.sender_email,
                to=[str(self.receiver_email)],
                reply_to=None
            )
            # Use fail_silently=True to prevent blocking user registration
            msg.send(fail_silently=True)
            print(f"Account activation email sent successfully to {self.receiver_email}")
            return True
        except Exception as e:
            print(f"Failed to send account activation email to {self.receiver_email}: {e}")
            # Don't raise exception to avoid blocking registration
            return False

    def account_activation_status_mail(self):
        try:
            msg = EmailMessage(
                subject=self.email_subject,
                body=self.email_body,
                from_email=self.sender_email,
                to=[str(self.receiver_email)],
                reply_to=None
            )
            msg.send(fail_silently=False)
            print(f"Account activation status email sent successfully to {self.receiver_email}")
            return True
        except Exception as e:
            print(f"Failed to send account activation status email to {self.receiver_email}: {e}")
            raise e

    def password_reset_mail(self):
        try:
            msg = EmailMessage(
                subject=self.email_subject,
                body=self.email_body,
                from_email=self.sender_email,
                to=[str(self.receiver_email)],
                reply_to=None
            )
            msg.send(fail_silently=False)
            print(f"Password reset email sent successfully to {self.receiver_email}")
            return True
        except Exception as e:
            print(f"Failed to send password reset email to {self.receiver_email}: {e}")
            raise e

    def contact_us_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return "Done"

    def order_booking_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return "Done"

    def export_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return True

    def import_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return True

    def local_logistics_mail(self):
        send_mail(
            subject=self.email_subject,
            message=self.email_body,
            from_email=self.sender_email,
            recipient_list=[self.receiver_email,],
            fail_silently=False,
        )
        return True
