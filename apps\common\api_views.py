from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from core import tasks
from core.settings import base
from apps.users.models import CustomUser
from apps.company.models import Company
from apps.exports.models import Export
from apps.imports.models import Import
from apps.logistics.models import Logistic
from .serializers import (
    AppConfigurationSerializer,
    ContactFormSerializer,
    TrackingSerializer,
    SystemStatsSerializer
)


class AppConfigurationAPIView(APIView):
    """API View to get app configuration data"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def get(self, request):
        """Get app configuration data"""
        config_data = {
            'countries': [
                {'code': 'NG', 'name': 'Nigeria'},
                {'code': 'US', 'name': 'United States'},
                {'code': 'GB', 'name': 'United Kingdom'},
                {'code': 'CA', 'name': 'Canada'},
                {'code': 'GH', 'name': 'Ghana'},
                {'code': 'KE', 'name': 'Kenya'},
                {'code': 'ZA', 'name': 'South Africa'},
            ],
            'weight_units': [
                {'code': 'Kg', 'name': 'Kilograms'},
                {'code': 'Lbs', 'name': 'Pounds'},
                {'code': 'Mts', 'name': 'Metric Tons'},
                {'code': 'Ton', 'name': 'Tons'},
            ],
            'document_types': [
                {'code': 'passport', 'name': 'International Passport'},
                {'code': 'drivers_license', 'name': "Driver's License"},
                {'code': 'national_id', 'name': 'National ID Card'},
                {'code': 'voters_card', 'name': "Voter's Card"},
            ],
            'user_types': [
                {'code': 'regular', 'name': 'Regular User'},
                {'code': 'small business', 'name': 'Small Business'},
                {'code': 'dispatcher', 'name': 'Dispatcher'},
            ],
            'shipping_statuses': [
                {'code': 'Pending', 'name': 'Pending'},
                {'code': 'In Transit', 'name': 'In Transit'},
                {'code': 'Delivered', 'name': 'Delivered'},
                {'code': 'Cancelled', 'name': 'Cancelled'},
            ],
            'payment_statuses': [
                {'code': 'Unpaid', 'name': 'Unpaid'},
                {'code': 'Paid', 'name': 'Paid'},
                {'code': 'Failed', 'name': 'Failed'},
                {'code': 'Refunded', 'name': 'Refunded'},
            ],
            'delivery_statuses': [
                {'code': 'Pending', 'name': 'Pending'},
                {'code': 'In Transit', 'name': 'In Transit'},
                {'code': 'Delivered', 'name': 'Delivered'},
                {'code': 'Cancelled', 'name': 'Cancelled'},
            ]
        }
        
        serializer = AppConfigurationSerializer(config_data)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class ContactFormAPIView(APIView):
    """API View for contact form submission"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def post(self, request):
        """Submit contact form"""
        serializer = ContactFormSerializer(data=request.data)
        
        if serializer.is_valid():
            data = serializer.validated_data
            
            # Prepare email context
            context = {
                'name': data['name'],
                'email': data['email'],
                'subject': data['subject'],
                'message': data['message']
            }
            
            # Send email to admin
            html_message = render_to_string('emails/contact_form.html', context)
            message = strip_tags(html_message)
            
            # Send email asynchronously
            tasks.send_contact_form_mail_task.delay(
                subject=f"Contact Form: {data['subject']}",
                message=message,
                sender_email=data['email'],
                receiver_email=base.DEFAULT_FROM_EMAIL
            )
            
            return Response({
                'success': True,
                'message': 'Your message has been sent successfully. We will get back to you soon.'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': 'Contact form submission failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UnifiedTrackingAPIView(APIView):
    """API View for unified shipment tracking"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def get(self, request, tracking_number):
        """Track shipment by tracking number across all shipment types"""
        tracking_number = tracking_number.upper()
        
        # Try to find in exports
        try:
            export = Export.objects.select_related('sender', 'destination').get(
                tracking_no=tracking_number
            )
            return self._format_export_response(export)
        except Export.DoesNotExist:
            pass
        
        # Try to find in imports
        try:
            import_shipment = Import.objects.select_related('sender', 'destination').get(
                tracking_no=tracking_number
            )
            return self._format_import_response(import_shipment)
        except Import.DoesNotExist:
            pass
        
        # Try to find in logistics
        try:
            logistic = Logistic.objects.select_related('company', 'sender').get(
                tracking_no=tracking_number
            )
            return self._format_logistic_response(logistic)
        except Logistic.DoesNotExist:
            pass
        
        # Not found in any shipment type
        return Response({
            'success': False,
            'message': 'No shipment found with this tracking number'
        }, status=status.HTTP_404_NOT_FOUND)
    
    def _format_export_response(self, export):
        """Format export tracking response"""
        return Response({
            'success': True,
            'data': {
                'tracking_no': export.tracking_no,
                'type': 'export',
                'status': export.shipping_status,
                'sender': {
                    'name': export.sender.name if export.sender else None,
                    'address': export.sender.get_short_address() if export.sender else None
                },
                'destination': {
                    'name': export.destination.name if export.destination else None,
                    'address': export.destination.get_short_address() if export.destination else None
                },
                'weight': export.get_weight,
                'shipped_at': export.shipped_at,
                'estimated_delivery': None,
                'tracking_history': [
                    {
                        'status': 'Shipment Created',
                        'timestamp': export.created_on,
                        'location': export.sender.get_short_address() if export.sender else 'Origin'
                    },
                    {
                        'status': export.shipping_status,
                        'timestamp': export.shipped_at or export.updated_on,
                        'location': 'In Transit' if export.shipping_status != 'Pending' else 'Origin'
                    }
                ]
            }
        }, status=status.HTTP_200_OK)
    
    def _format_import_response(self, import_shipment):
        """Format import tracking response"""
        return Response({
            'success': True,
            'data': {
                'tracking_no': import_shipment.tracking_no,
                'type': 'import',
                'status': import_shipment.shipping_status,
                'sender': {
                    'name': import_shipment.sender.name if import_shipment.sender else None,
                    'address': import_shipment.sender.get_short_address() if import_shipment.sender else None
                },
                'destination': {
                    'name': import_shipment.destination.name if import_shipment.destination else None,
                    'address': import_shipment.destination.get_short_address() if import_shipment.destination else None
                },
                'weight': import_shipment.get_weight,
                'shipped_at': import_shipment.shipped_at,
                'estimated_delivery': None,
                'tracking_history': [
                    {
                        'status': 'Shipment Created',
                        'timestamp': import_shipment.created_on,
                        'location': import_shipment.sender.get_short_address() if import_shipment.sender else 'Origin'
                    },
                    {
                        'status': import_shipment.shipping_status,
                        'timestamp': import_shipment.shipped_at or import_shipment.updated_on,
                        'location': 'In Transit' if import_shipment.shipping_status != 'Pending' else 'Origin'
                    }
                ]
            }
        }, status=status.HTTP_200_OK)
    
    def _format_logistic_response(self, logistic):
        """Format logistic tracking response"""
        return Response({
            'success': True,
            'data': {
                'tracking_no': logistic.tracking_no,
                'type': 'logistics',
                'status': logistic.delivery_status,
                'company': logistic.company.business_name,
                'sender': {
                    'name': logistic.sender.name if logistic.sender else None,
                    'address': logistic.sender.get_short_address() if logistic.sender else None
                },
                'destination': {
                    'name': logistic.receiver_name,
                    'address': logistic.get_receiver_address()
                },
                'weight': logistic.get_weight,
                'shipped_at': logistic.shipped_at,
                'estimated_delivery': None,
                'tracking_history': [
                    {
                        'status': 'Order Created',
                        'timestamp': logistic.created_on,
                        'location': logistic.sender.get_short_address() if logistic.sender else 'Origin'
                    },
                    {
                        'status': logistic.delivery_status,
                        'timestamp': logistic.shipped_at or logistic.updated_on,
                        'location': 'In Transit' if logistic.delivery_status != 'Pending' else 'Origin'
                    }
                ]
            }
        }, status=status.HTTP_200_OK)


class SystemStatsAPIView(APIView):
    """API View for system-wide statistics (Admin only)"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get system-wide statistics"""
        # Get counts
        total_users = CustomUser.objects.count()
        total_companies = Company.objects.count()
        total_exports = Export.objects.count()
        total_imports = Import.objects.count()
        total_logistics = Logistic.objects.count()
        total_shipments = total_exports + total_imports + total_logistics
        
        # Get recent activities (last 10)
        recent_activities = []
        
        # Recent exports
        recent_exports = Export.objects.order_by('-created_on')[:3]
        for export in recent_exports:
            recent_activities.append({
                'type': 'export',
                'action': 'created',
                'tracking_no': export.tracking_no,
                'user': f"{export.customer.first_name} {export.customer.last_name}",
                'timestamp': export.created_on
            })
        
        # Recent imports
        recent_imports = Import.objects.order_by('-created_on')[:3]
        for import_item in recent_imports:
            recent_activities.append({
                'type': 'import',
                'action': 'created',
                'tracking_no': import_item.tracking_no,
                'user': f"{import_item.customer.first_name} {import_item.customer.last_name}",
                'timestamp': import_item.created_on
            })
        
        # Recent logistics
        recent_logistics = Logistic.objects.order_by('-created_on')[:3]
        for logistic in recent_logistics:
            recent_activities.append({
                'type': 'logistics',
                'action': 'created',
                'tracking_no': logistic.tracking_no,
                'company': logistic.company.business_name,
                'timestamp': logistic.created_on
            })
        
        # Sort by timestamp
        recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
        recent_activities = recent_activities[:10]
        
        stats_data = {
            'total_users': total_users,
            'total_companies': total_companies,
            'total_exports': total_exports,
            'total_imports': total_imports,
            'total_logistics': total_logistics,
            'total_shipments': total_shipments,
            'recent_activities': recent_activities
        }
        
        serializer = SystemStatsSerializer(stats_data)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
