from django.contrib import admin
from .models import *
from phonenumber_field.widgets import PhoneNumberPrefixWidget
from django import forms


class AddressForm(forms.ModelForm):
    class Meta:
        widgets = {'phone_number': PhoneNumberPrefixWidget(initial="NGN")}


@admin.register(Address)
class AdminAddress(admin.ModelAdmin):
    form = AddressForm
    list_display = ('name', 'address', 'city', 'state', 'country', 'phone_number')
    search_fields = ('city', 'state', 'name', 'address', 'country', 'phone_number')
    readonly_fields = ('id',)
