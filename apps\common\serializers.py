from rest_framework import serializers


class AppConfigurationSerializer(serializers.Serializer):
    """Serializer for app configuration data"""
    countries = serializers.ListField()
    weight_units = serializers.ListField()
    document_types = serializers.ListField()
    user_types = serializers.ListField()
    shipping_statuses = serializers.ListField()
    payment_statuses = serializers.ListField()
    delivery_statuses = serializers.ListField()


class ContactFormSerializer(serializers.Serializer):
    """Serializer for contact form submission"""
    name = serializers.CharField(max_length=100, required=True)
    email = serializers.EmailField(required=True)
    subject = serializers.CharField(max_length=200, required=True)
    message = serializers.CharField(required=True)
    
    def validate_message(self, value):
        """Validate message length"""
        if len(value) < 10:
            raise serializers.ValidationError("Message must be at least 10 characters long.")
        return value


class TrackingSerializer(serializers.Serializer):
    """Serializer for unified tracking"""
    tracking_number = serializers.Char<PERSON>ield(max_length=50, required=True)
    
    def validate_tracking_number(self, value):
        """Validate tracking number format"""
        if len(value) < 5:
            raise serializers.ValidationError("Invalid tracking number format.")
        return value.upper()


class SystemStatsSerializer(serializers.Serializer):
    """Serializer for system-wide statistics"""
    total_users = serializers.IntegerField()
    total_companies = serializers.IntegerField()
    total_exports = serializers.IntegerField()
    total_imports = serializers.IntegerField()
    total_logistics = serializers.IntegerField()
    total_shipments = serializers.IntegerField()
    recent_activities = serializers.ListField()
