from .mail_helper import SendEmailClass


def send_activation_email(user, subject, message, sender_email):
    """
    Send activation email synchronously (no Celery dependency)
    """
    try:
        # Send email synchronously only
        email_sender = SendEmailClass(
            email_subject=subject,
            email_body=message,
            sender_email=sender_email,
            receiver_email=user.email,
            reply_to=None
        )
        email_sender.account_activation_mail()
        return True, "Em<PERSON> sent successfully"
        
    except Exception as e:
        print(f"Failed to send activation email to {user.email}: {e}")
        # Return success anyway to not block registration
        return True, f"Registration completed (email delivery may have failed: {str(e)})"


def send_export_notification_email(subject, message, sender_email, receiver_email):
    """
    Send export notification email synchronously (no Celery dependency)
    """
    try:
        # Send email synchronously only
        email_sender = SendEmailClass(
            email_subject=subject,
            email_body=message,
            sender_email=sender_email,
            receiver_email=receiver_email,
            reply_to=None
        )
        email_sender.export_mail()
        return True, "Em<PERSON> sent successfully"
        
    except Exception as e:
        print(f"Failed to send export notification email to {receiver_email}: {e}")
        return False, f"Failed to send email: {str(e)}"


def send_import_notification_email(subject, message, sender_email, receiver_email):
    """
    Send import notification email synchronously (no Celery dependency)
    """
    try:
        # Send email synchronously only
        email_sender = SendEmailClass(
            email_subject=subject,
            email_body=message,
            sender_email=sender_email,
            receiver_email=receiver_email,
            reply_to=None
        )
        email_sender.import_mail()
        return True, "Email sent successfully"
        
    except Exception as e:
        print(f"Failed to send import notification email to {receiver_email}: {e}")
        return False, f"Failed to send email: {str(e)}"