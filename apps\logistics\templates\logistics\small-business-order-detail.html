{% extends 'dashboard.html' %}
{% load humanize %}
{% block title %} Order details {% endblock title %}



{% block content %}
    
    <main role="main" class="container main bg-gold">

        <h3>View Orders</h3>
        <p>
            <a class="link" href="{% url 'user_profile' %}">Home</a> | 
            <a class="link" href="{% url 'company_order_list' %}">order</a></h1> |
            {{ order.tracking_no }}
        </p>
        <hr>
        
        <!--order basic info-->
        <div class="row mt-2 justify-content-center">
            <div class="col-12 col-sm-6 col-md-6 col-lg-6 cards mb-3">
                <div class="card">
                    <h5 class="m-2 mt-3">{{ order.tracking_no }}</h5>
                    <hr>
                    <div class="row p-2 cards">
                        <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                            <div class="sender-detail">
                                <h5>Sender Details: </h5>
                                <b>{{ order.sender.name|capfirst}}</b>
                                <p>{{ order.sender.get_address}}</p>
                            </div>
                        </div>
    
                        <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                            <div class="receiver-detail">
                                <h5>Customer: </h5>
                                <b>{{ order.company|capfirst }}</b>
                                <p>{{ order.company.company_address|capfirst }}</p>
                            </div>
                        </div>

                        <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                            <div class="receiver-detail">
                                <h6>Destination Warehouse: </h6>
                                <p><small>{{ order.destination.get_address }}</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>Origin Warehouse: </h6>
                                <p><small>{{ order.sender.city|capfirst }}, {{ order.sender.state|capfirst }}, {{ order.sender.country|capfirst }}</small></p>
                            </div>
                        </div>

                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>Weight: </h6>
                                <p ><small>{{ order.get_weight}}</small></p>
                            </div>
                        </div>

                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>Volumentary Weight: </h6>
                                <p><small>{{ order.get_volumentary_weight }}</small></p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>No. of Deliveries: </h6>
                                <p><small>{{ total_items }}</small></p>
                            </div>
                        </div>

                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>Date created: </h6>
                                <p><small>{{ order.created_on }}</small></p>
                            </div>
                        </div>

                        <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                            <div class="p-2 other-details">
                                <h6>Last Updated: </h6>
                                <p><small>{{ order.updated_on}}</small></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!--items list table-->
            <div class="col-12 col-sm-6 col-md-6 col-lg-6 cards mb-3">
                <div class="card">
                    <h3 class="p-2 pt-2 pb-1">Delivery List</h3>
                    <hr>
                    <div class="table-responsive p-2 pt-2 pb-0" >
                        {% if items %}
                            <table class="table table-sm table-striped table-dark rounded">
                                <thead>
                                    <tr>
                                        <th scope="col">Name</th>
                                        <th scope="col">Quantity</th>
                                        <th scope="col">Price per item</th>
                                        <th scope="col">Weight</th>
                                        <th scope="col">Total Amount</th>
                                        {% comment %} <th scope="col"></th> {% endcomment %}
                                    </tr>
                                </thead>
                                <tbody class="table-group-divider">
                                    {% for item in items %}
                                        <tr scope="row">
                                            <td>{{ item.name|capfirst }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.price }}</td>
                                            <td>{{ item.weight }}</td>
                                            <td>{{ item.total_price }}</td>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% else %}
                            <h1 class="text-center">You do not have any item available on this order.</h1>
                        {% endif %}
                    </div>

                    <div class="p-2 pt-0 text-center">
                        <span class="step-links">
                            {% if items.has_previous %}
                                <a href="?page=1" class="btn btn-primary">first</a>
                                    <span class="text-primary">
                                        Page {{ items.number }} of {{ items.paginator.num_pages }}.
                                    </span>
                                <a href="?page={{ items.previous_page_number }}" class="btn btn-success">previous</a>
                            {% endif %}
                    
                            {% if items.has_next %}
                                <a href="?page={{ items.next_page_number }}" class="btn btn-primary">Next</a>
                                    <span class="text-primary">
                                        Page {{ items.number }} of {{ items.paginator.num_pages }}.
                                    </span>
                                <a href="?page={{ items.paginator.num_pages }}" class="btn btn-success">Last</a>
                            {% endif %}
                        </span>
                    </div>
                    {% if total_items %}
                        <h5 class="p-2 pt-1 pb-1">Total Items: {{ total_items }}</h5>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                <div class="card text-center w-100 h-100">
                    <h4 class="mt-3">Delivery Status</h4>
                    <hr>
                    <td><span class="btn btn-secondary"><a href="{{ order.tracking_url }}">Track delivery.</a></span></td>
                </div>
            </div>
            
            <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                <div class="card text-center w-100 h-100">
                    <h4 class="mt-3">Attention!</h4>
                    <hr>
                    {% if order.attention %}
                        <div class="p-4">
                            <p>{{ order.attention|capfirst }}</p>
                        </div>
                    {% else %}
                        <div class="p-4">
                            <p>You dont have any actions to take yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!--Payment status column-->
            <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                <div class="card text-center p-2">
                    <h2>Payment Status</h2>
                    {% if order.payment_status == "unpaid" %}
                        <div class="w-100 text-danger">
                            <h1 class="">{{ order.payment_status|capfirst }}</h1>
                        </div>
                        <hr>
                        <p>Amount To Pay:</p>
                        <h3>NGN{{ order.delivery_fee|floatformat:"2g" }}</h3>
                        <div class="w-100">
                            <a class="btn btn-primary" href="{% url 'make_order_payment' %}">Pay with Paystack</a>
                        </div>
                    {% elif order.payment_status == "pending" %}
                        <div class="w-100 text-info">
                            <h1 class="">{{ order.payment_status|capfirst }}</h1>
                        </div>
                        <hr>
                        <p>Amount To be confirmed:</p>
                        <h3>NGN{{ order_fee|floatformat:"2g" }}</h3>
                        <div class="w-100">
                            <a class="btn btn-info disabled">Pending confirmation</a>
                        </div>
                    {% elif order.payment_status == "failed" %}
                        <div class="w-100 text-danger">
                            <h1 class="">{{ order.payment_status|capfirst }}</h1>
                        </div>
                        <hr>
                        <p>Amount To Pay:</p>
                        <h3>NGN{{ order.delivery_fee|floatformat:"2g" }}</h3>
                        <div class="w-100">
                            <a class="btn btn-primary" href="{% url 'make_order_payment' %}">Pay with Paystack</a>
                        </div>
                    {% else %}
                        <div class="w-100 text-success">
                            <h1 class="">{{ order.payment_status|capfirst }}</h1>
                        </div>
                        <hr>
                        <p>Amount Confirmed:</p>
                        <h3>NGN{{ order.delivery_fee|floatformat:"2g" }}</h3>
                        <div class="w-100">
                            <a class="btn btn-success disabled" href="">Payment Confirmed</a>
                        </div>
                    {% endif %}
                </div>
            </div> 
        </div>
    </main>
{% endblock content %}