from django.contrib import admin
from .models import Logistic, Item
from core import tasks
from core.settings import base
from django.urls import reverse


# Automatically triggers the model object and sends mails to its associated user's email.
def send_notification_email(modeladmin, request, queryset):
    for order in queryset:
        tracking_url = f'{request.get_host()}{reverse("track_import_shipping")}'
        custom_tracking_url = f'{request.get_host()}{reverse("import_search_result")}?tracking-no={order.tracking_no}'
        import_url = f'{request.get_host()}{order.get_absolute_url()}'
        email = order.customer.email
        subject = f'Local order {order.tracking_no} placed.'
        message = f"""
            Your order has been processed.Your tracking number is {order.tracking_no}.
            Visit {import_url} to view your order history and make outstanding payment.
            If you do not have an account with us yet, you can visit {tracking_url} to track your order
            with your tracking number or visit {custom_tracking_url} directly to track your order.
        """
        if order.email_sent == True:
            continue
        tasks.send_export_mail_task.delay(
            subject=subject,
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL,
        )
        order.email_sent = True
        order.save()
    message = f'Local delivery notification email successfully sent to {email}.'
    modeladmin.message_user(request, message)


# Changes model object's associated email_sent status to False.
def set_mailing_status_to_false(modeladmin, request, queryset):
    for order in queryset:
        order.email_sent = False
        order.save()
    message = 'status changed to {0}'.format(order.email_sent)
    modeladmin.message_user(request, message)


@admin.register(Item)
class AdminItem(admin.ModelAdmin):
    list_display = ('logistics', 'name', 'quantity', 'price', 'total_price', 'weight')
    readonly_fields = ('id', 'total_price')
    search_fields = ('logistics__tracking_no', 'logistics__company__business_name', 'name', 'logistics__company__user__first_name',
                     'logistics__company__user__last_name')

    def company_full_name(self, obj):
        return f"{obj.company.user.first_name} {obj.company.user.last_name}"

    # Enable sorting by full name
    company_full_name.admin_order_field = 'logistics__company__user__last_name'


@admin.register(Logistic)
class AdminLogistic(admin.ModelAdmin):
    list_display = ('id', 'company', 'sender', 'receiver_name', 'phone_no', 'display_weight', 'display_volumentary_weight',
                    'delivery_fee', 'payment_status', 'tracking_no', 'payment_reference', 'email_sent', "dispatcher")
    search_fields = ('tracking_no', 'company__user__email', 'payment_reference', 'payment_status',
                     'company__business_name', 'company__user__first_name', 'company__user__last_name')
    readonly_fields = ('tracking_no', 'payment_reference', 'display_weight', 'display_volumentary_weight')
    actions = [send_notification_email, set_mailing_status_to_false]

    def display_weight(self, obj):
        return obj.get_weight
    display_weight.short_description = "weight"

    def display_volumentary_weight(self, obj):
        return obj.get_volumentary_weight
    display_volumentary_weight.short_description = "volumentary weight"

    def company_full_name(self, obj):
        return f"{obj.company.user.first_name} {obj.company.user.last_name}"

    # Enable sorting by full name
    company_full_name.admin_order_field = 'company__user__last_name'
