# MOG Dynamics API - Postman Setup Guide

## 📁 Files Created

I've created the following files for easy API testing:

1. **`MOG_Dynamics_API.postman_collection.json`** - Complete API collection
2. **`MOG_Dynamics_Local.postman_environment.json`** - Local development environment
3. **`POSTMAN_SETUP_GUIDE.md`** - This setup guide

## 🚀 Quick Setup Instructions

### Step 1: Import Collection & Environment

1. **Open Postman**
2. **Import Collection**:
   - Click "Import" button
   - Select `MOG_Dynamics_API.postman_collection.json`
   - Click "Import"

3. **Import Environment**:
   - Click "Import" button  
   - Select `MOG_Dynamics_Local.postman_environment.json`
   - Click "Import"

4. **Select Environment**:
   - In top-right corner, select "MOG Dynamics - Local Development"

### Step 2: Start Your Django Server

```bash
cd /path/to/your/mog_dynamics
python manage.py runserver 127.0.0.1:8000
```

### Step 3: Test the Setup

1. **Test Basic Connection**:
   - Go to "Static Data" → "Get App Configuration"
   - Click "Send"
   - Should return 200 OK (even if endpoint doesn't exist yet)

## 📋 Collection Structure

The Postman collection includes these sections:

### 🔐 Authentication
- **Register User** - Create new user account
- **Login** - Get JWT tokens (auto-saves to environment)
- **Refresh Token** - Refresh access token
- **Account Activation** - Activate user account

### 👤 User Management  
- **Get User Profile** - Fetch user details
- **Update User Profile** - Update user information
- **Get Dashboard Stats** - User dashboard data

### 🏢 Company Management
- **Register Company** - Register business
- **Get User Companies** - List user's companies

### 📦 Export Shipments
- **Create Export Shipment** - Create new export
- **Get Export List** - List user's exports
- **Get Export Details** - Get specific export

### 📥 Import Shipments
- **Create Import Shipment** - Create new import
- **Get Import List** - List user's imports  
- **Get Import Details** - Get specific import

### 📍 Address Management
- **Create Address** - Add new address
- **Get User Addresses** - List user's addresses

### 💳 Payment Processing
- **Initiate Payment** - Start payment process
- **Verify Payment** - Confirm payment status

### 🔍 Shipment Tracking
- **Track Shipment (Public)** - Track by tracking number

### 📊 Static Data
- **Get Pricing Information** - Fetch pricing data
- **Get Gallery Images** - Get gallery images
- **Get App Configuration** - Get app settings

## 🔧 Environment Variables

The environment includes these variables:

- `base_url` - API base URL (http://127.0.0.1:8000)
- `access_token` - JWT access token (auto-populated)
- `refresh_token` - JWT refresh token (auto-populated)
- `user_id` - Current user ID
- `export_id` - Sample export ID for testing
- `import_id` - Sample import ID for testing
- `logistics_id` - Sample logistics ID for testing
- `address_id` - Sample address ID for testing
- `company_id` - Sample company ID for testing
- `tracking_number` - Sample tracking number

## 🎯 Testing Workflow

### 1. Authentication Flow
```
1. Register User → Creates account
2. Login → Gets tokens (auto-saved)
3. Get User Profile → Verify authentication works
```

### 2. Basic Operations Flow
```
1. Create Address → Save address_id to environment
2. Register Company → Save company_id to environment  
3. Create Export/Import → Save shipment_id to environment
4. Get Lists → Verify data appears
```

### 3. Payment Flow
```
1. Create Shipment → Get shipment ID
2. Initiate Payment → Get payment URL
3. Verify Payment → Confirm payment status
```

## 🔄 Auto-Token Management

The collection includes scripts that automatically:

- **Save tokens** after successful login
- **Refresh tokens** when they expire
- **Include Bearer token** in authenticated requests

## 📝 Sample Data

The collection includes realistic sample data:

### User Registration
```json
{
  "first_name": "John",
  "last_name": "Doe", 
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phone_no": "+*************",
  "city": "Lagos",
  "state": "Lagos",
  "country": "Nigeria"
}
```

### Export Shipment
```json
{
  "weight": 25.5,
  "weight_unit": "Kg",
  "items": [
    {
      "name": "Electronics",
      "quantity": 2,
      "price": 500.00,
      "weight": 12.5
    }
  ]
}
```

## 🛠 Customization

### Change Base URL
1. Go to Environment settings
2. Update `base_url` value
3. Save environment

### Add New Variables
1. Click environment name
2. Add new variable
3. Use `{{variable_name}}` in requests

### Modify Sample Data
1. Open any request
2. Edit the Body/Form data
3. Save request

## 🚨 Important Notes

### Before Testing:
1. **Django server must be running** on http://127.0.0.1:8000
2. **Database must be set up** and migrated
3. **Environment must be selected** in Postman

### Authentication:
- Most endpoints require authentication
- Login first to get tokens
- Tokens are automatically included in requests
- Refresh token when access token expires

### File Uploads:
- Use "form-data" for file uploads
- Select files in Postman interface
- Supported formats: PNG, JPEG, JPG, PDF

### Error Handling:
- Check response status codes
- Review error messages in response body
- Verify request format matches API documentation

## 🔍 Troubleshooting

### Common Issues:

**401 Unauthorized**
- Login to get fresh tokens
- Check if access token is set in environment

**404 Not Found**  
- Verify Django server is running
- Check if API endpoints are implemented
- Confirm URL structure

**500 Internal Server Error**
- Check Django server logs
- Verify database connection
- Check for missing migrations

**File Upload Issues**
- Use multipart/form-data content type
- Select actual files in Postman
- Check file size limits

## 📚 Next Steps

1. **Import the files** into Postman
2. **Start your Django server**
3. **Test authentication** endpoints first
4. **Create sample data** using the requests
5. **Verify responses** match expected format
6. **Customize requests** for your specific needs

This Postman collection provides a complete testing environment for your MOG Dynamics API, making it easy to test all endpoints during development and integration with your Flutter app.
