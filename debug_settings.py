#!/usr/bin/env python
import os
import sys
import django
from pathlib import Path

# Add the project directory to the Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set the Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings.dev")

# Setup Django
django.setup()

from django.conf import settings

print("=== DJANGO SETTINGS DEBUG ===")
print(f"Settings module: {settings.SETTINGS_MODULE}")
print(f"Base dir: {settings.BASE_DIR}")
print(f"Debug: {settings.DEBUG}")

print("\n=== DATABASE CONFIGURATION ===")
print(f"DATABASES: {settings.DATABASES}")

print("\n=== ENVIRONMENT VARIABLES ===")
print(f"SECRET_KEY from env: {os.environ.get('SECRET_KEY', 'NOT_FOUND')}")
print(f"DATABASE_NAME from env: {os.environ.get('DATABASE_NAME', 'NOT_FOUND')}")

print("\n=== INSTALLED APPS ===")
for app in settings.INSTALLED_APPS:
    print(f"  - {app}")
