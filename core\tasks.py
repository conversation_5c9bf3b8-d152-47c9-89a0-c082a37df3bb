from celery import shared_task
from apps.common import mail_helper


@shared_task
def send_account_activation_mail_task(subject, message, sender_email, receiver_email):
    contact_us = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=None
    )
    try:
        contact_us.account_activation_mail()
    except Exception as e:
        print(f'{e}: Could not send activation mail to {receiver_email}')
    return f"Activation mail sent successfully to {receiver_email}"


@shared_task
def send_account_activation_status_mail_task(subject, message, sender_email, receiver_email):
    contact_us = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=None
    )
    try:
        contact_us.account_activation_status_mail()
    except Exception as e:
        print(f'{e}: Could not send account activation status mail to {receiver_email}')
    return f"Account activation status mail sent successfully to {receiver_email}"


@shared_task
def send_contact_mail_task(subject, message, sender_email, receiver_email, reply_to):
    contact_us = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=reply_to
    )
    try:
        contact_us.contact_us_mail()
    except Exception as e:
        print(f'{e}: Could not send contact us mail to {receiver_email}')
    return f"Contact mail sent successfully to {receiver_email}"


@shared_task
def send_order_booking_email_task(subject, message, receiver_email, from_email, reply_to):
    order_booking = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        sender_email=from_email,
        receiver_email=receiver_email,
        reply_to=reply_to
    )
    try:
        order_booking.order_booking_mail()
    except Exception as e:
        print(f'{e}: Could not send order booking mail to {receiver_email}')
    return f"Online booking mail sent successfully to {receiver_email}"


@shared_task
def send_password_reset_task(subject, message, sender_email, receiver_email):
    password_reset = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=None
    )
    try:
        password_reset.password_reset_mail()
    except Exception as e:
        print(f"{e}: Could not send password reset mail to {receiver_email}")
    return f"Password reset email sent to {receiver_email}"


@shared_task
def send_export_mail_task(subject, message, sender_email, receiver_email):
    send_export_mail = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=None
    )
    try:
        send_export_mail.export_mail()
    except Exception as e:
        print(f"{e}: Could not send export notification mail to {receiver_email}")
    return f"Export notification email sent to {receiver_email}"


@shared_task
def send_import_mail_task(subject, message, sender_email, receiver_email):
    send_export_mail = mail_helper.SendEmailClass(
        email_subject=subject,
        email_body=message,
        receiver_email=receiver_email,
        sender_email=sender_email,
        reply_to=None
    )
    try:
        send_export_mail.import_mail()
    except Exception as e:
        print(f"{e}: Could not send import notification mail to {receiver_email}")
    return f"Import notification email sent to {receiver_email}"
