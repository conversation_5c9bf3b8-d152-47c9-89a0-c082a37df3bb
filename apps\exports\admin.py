from django import forms
from django.contrib import admin
from .models import Export, Item
from phonenumber_field.widgets import PhoneNumberPrefixWidget
from core.settings import base
from apps.common.simple_email import send_simple_notification_email


# Autimatically triggers the model object and sends mails to it
def send_notification_email(modeladmin, request, queryset):
    for order in queryset:
        export_url = f'{request.get_host()}{order.get_absolute_url()}'
        email = order.customer.email
        subject = f'Export order {order.tracking_no} placed.'
        message = f"""
            Your order has been processed.Your tracking number is {order.tracking_no}.
            Visit {export_url} to view your order history and make outstanding payment.
        """
        if order.email_sent:
            continue
        
        success = send_simple_notification_email(
            recipient_email=email,
            subject=subject,
            message=message,
            from_email=base.DEFAULT_FROM_EMAIL,
        )
        
        if success:
            order.email_sent = True
            order.save()
    
    message = 'Export notification email sent successfully.'
    modeladmin.message_user(request, message)

# Changes email_sent status to false.


def set_mailing_status_to_false(modeladmin, request, queryset):
    for order in queryset:
        order.email_sent = False
        order.save()
    message = 'status changed to {0}'.format(order.email_sent)
    modeladmin.message_user(request, message)


class ExportForm(forms.ModelForm):
    class Meta:
        widgets = {'phone_no': PhoneNumberPrefixWidget(initial="NGN")}


@admin.register(Item)
class AdminItem(admin.ModelAdmin):
    list_display = ('export', 'name', 'quantity', 'price', 'total_price', 'weight')
    readonly_fields = ('id', 'total_price')
    search_fields = ('export__tracking_no', 'name', 'export__customer__first_name',
                     'export__customer__last_name', 'export__customer__profile_code')

    def customer_full_name(self, obj):
        return f"{obj.export.customer.first_name} {obj.export.customer.last_name}"

    # Enable sorting by full name
    customer_full_name.admin_order_field = 'export__customer__last_name'


@admin.register(Export)
class AdminExport(admin.ModelAdmin):
    form = ExportForm
    list_display = ('customer', 'sender', 'phone_no', 'display_weight', 'display_volumentary_weight',
                    'export_fee', 'payment_status', 'tracking_no', 'payment_reference', 'email_sent')
    search_fields = (
        "tracking_no",
        "customer__email",
        "payment_reference",
        "payment_status",
        "customer__first_name",
        "customer__last_name",
        "customer__profile_code",
        "sender__city",
        "sender__country",
        "phone_no",
        "weight"
    )
    readonly_fields = ('tracking_no', 'payment_reference', 'display_weight', 'display_volumentary_weight')
    actions = [send_notification_email, set_mailing_status_to_false]

    def display_weight(self, obj):
        return obj.get_weight
    display_weight.short_description = "weight"

    def display_volumentary_weight(self, obj):
        return obj.get_volumentary_weight
    display_volumentary_weight.short_description = "volumentary weight"

    def customer_full_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}"

    # Enable sorting by full name
    customer_full_name.admin_order_field = 'customer__last_name'
