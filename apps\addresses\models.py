from django.db import models
from apps.common import choices
from django.urls import reverse
from apps.common.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField


class Address(TimeStampedModel):
    name = models.CharField(max_length=150, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(choices=choices.country_choices, max_length=100)
    phone_number = PhoneNumberField(blank=True, max_length=14)

    def __str__(self):
        return f"{self.name} {self.state.capitalize()} {self.country.capitalize()}"

    def get_absolute_url(self, *args, **kwargs):
        return reverse("address_detail", kwargs={"id": self.id})

    def get_address(self):
        return f"{self.address.capitalize()}, {self.city.capitalize()}, {self.state.capitalize()}, {self.country.capitalize()}."

    def get_short_address(self):
        return f"{self.city.capitalize()}, {self.state.capitalize()}, {self.country.capitalize()}."
