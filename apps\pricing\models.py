from django.db import models
from django.dispatch import receiver
from apps.common.custom_validators import custom_file_validator
from apps.common.models import TimeStampedModel
from django.db.models.signals import pre_delete
import cloudinary
from cloudinary.models import CloudinaryField


class Pricing(TimeStampedModel):
    title = models.CharField(max_length=100)
    image = CloudinaryField(folder="static/media/images/pricing/", use_filename=True,
                            max_length=500, validators=[custom_file_validator], blank=True, null=True)

    class Meta:
        verbose_name = "Pricing"
        verbose_name_plural = "Pricing"

    def __str__(self):
        return str(self.image)


@receiver(pre_delete, sender=Pricing)
def delete_model_object_and_associated_files(sender, instance, **kwargs):
    if instance.image:
        cloudinary.uploader.destroy(instance.image.public_id)
    else:
        pass
