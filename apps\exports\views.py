import requests
from .models import Export, Item
from django.db.models import Sum
from core.settings import base
from django.contrib import messages
from django.core.paginator import Paginator
from .paystack import initiate_payment
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from apps.common.decorators import is_disptacher
from django.db import transaction


# Export list by logged in user
@login_required(login_url="user_login")
@is_disptacher
def export_list(request):
    """
    Returns list of export objects including paginating the 
    lists in situations where the table grows.
    """
    template_name = "exports/export_list.html"
    total_exports = Export.objects.select_related('customer', 'sender').filter(customer=request.user).count()
    export_list = Export.objects.select_related('customer', 'sender').filter(customer=request.user)

    paginator = Paginator(export_list, 10)
    page = request.GET.get('page')
    exports = paginator.get_page(page)

    context = {"total_exports": total_exports, "exports": exports}
    return render(request, template_name, context)


# Returns details pertaining to an export object.
export_obj = ""


@login_required(login_url="user_login")
def export_detail(request, id):
    """
    Returns list of items and relating iformation about the export object.
    including paginating the lists in situations where the table grows
    """
    template_name = "exports/export_detail.html"
    export = get_object_or_404(Export, id=id)

    global export_obj
    export_obj = export
    total_items = Item.objects.select_related('export').filter(
        export=export).aggregate(total_items=Sum('quantity'))["total_items"]

    items_list = Item.objects.filter(export=export).order_by("created_on")
    paginator = Paginator(items_list, 5)
    page = request.GET.get('page')
    items = paginator.get_page(page)

    context = {"export": export, "items": items, "total_items": total_items}
    return render(request, template_name, context)


# initial payment
@login_required(login_url="user_login")
@transaction.atomic
def payment_view(request):
    template_name = "exports/payment.html"
    if request.method == 'GET':
        response = initiate_payment(request, export_obj)
        return response
    else:
        return render(request, template_name)


# verify payment status
@login_required(login_url="user_login")
@transaction.atomic
def verify_payment(request):
    transaction_reference = request.GET.get("reference")
    paystack_live_secret_key = str(base.PAYSTACK_LIVE_SECRET_KEY)
    # paystack_test_secret_key = str(base.PAYSTACK_TEST_SECRET_KEY)
    headers = {"Authorization": f"Bearer {paystack_live_secret_key}"}
    url = f"{base.PAYSTACK_BASE_URL}verify/{transaction_reference}"
    response = requests.get(url, headers=headers, timeout=30)
    response_data = response.json()['data']
    export_fee = float(export_obj.export_fee * 100)

    print(f"PAID EXPORT FEE: {export_fee}")

    if response_data['status'] == 'success' and response_data["amount"] == export_fee:
        Export.objects.select_for_update().filter(id=export_obj.id, payment_reference=transaction_reference).update(payment_status='paid')
        messages.success(request, f"payment transaction {response_data['status']}")
        return redirect("export_detail", export_obj.id)

    elif response_data['status'] == 'pending':
        Export.objects.select_for_update().filter(id=export_obj.id, payment_reference=transaction_reference).update(payment_status='pending')
        messages.info(request, f"payment transaction is {response_data['status']}")
        return redirect("export_detail", export_obj.id)

    if response_data['status'] == 'failed':
        Export.objects.select_for_update().filter(id=export_obj.id, payment_reference=transaction_reference).update(payment_status='failed')
        messages.error(request, f"payment transaction has {response_data['status']}")
        return redirect("export_detail", export_obj.id)

    messages.error(request, f"{response_data['status']}")
    return redirect("export_detail", export_obj.id)
