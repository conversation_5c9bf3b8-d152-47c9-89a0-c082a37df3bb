from django import forms
from django.contrib import admin
from django.db.models import Q
from django.utils.html import format_html
from django.urls import reverse
from phonenumber_field.widgets import PhoneNumberPrefixWidget
from core import tasks
from core.settings import base
from .models import ConsolidatedShipment, ConsolidatedItem, ShipmentType
from apps.imports.models import Import, Item as ImportItem
from apps.exports.models import Export, Item as ExportItem


def create_badge(color, text):
    """Helper function to create HTML badges"""
    return format_html(
        '<span style="background-color: {}; color: white; '
        'padding: 2px 6px; border-radius: 3px; font-size: 11px;">{}</span>',
        color, text
    )


class ConsolidatedShipmentForm(forms.ModelForm):
    class Meta:
        model = ConsolidatedShipment
        fields = '__all__'
        widgets = {'phone_no': PhoneNumberPrefixWidget(initial="NGN")}


class ConsolidatedItemInline(admin.TabularInline):
    model = ConsolidatedItem
    extra = 0
    readonly_fields = ('total_price',)
    fields = ('name', 'quantity', 'price', 'total_price', 'weight')


def send_consolidated_notification_email(modeladmin, request, queryset):
    """Send notification emails for consolidated shipments"""
    for shipment in queryset:
        if shipment.email_sent:
            continue
            
        shipment_url = f'{request.get_host()}{shipment.get_absolute_url()}'
        email = shipment.customer.email
        shipment_type = shipment.get_shipment_type_display()
        subject = f'{shipment_type} order {shipment.tracking_no} processed.'
        
        if shipment.is_import:
            tracking_url = f'{request.get_host()}{reverse("track_import_shipping")}'
            custom_tracking_url = (
                f'{request.get_host()}{reverse("import_search_result")}'
                f'?tracking-no={shipment.tracking_no}'
            )
            message = f"""
                Your {shipment_type.lower()} order has been processed. Your tracking number is {shipment.tracking_no}.
                Visit {shipment_url} to view your order history and make outstanding payment.
                If you do not have an account with us yet, you can visit {tracking_url} to track your order
                with your tracking number or visit {custom_tracking_url} directly to track your order.
            """
        else:
            message = f"""
                Your {shipment_type.lower()} order has been processed. Your tracking number is {shipment.tracking_no}.
                Visit {shipment_url} to view your order history and make outstanding payment.
            """
        
        tasks.send_export_mail_task.delay(
            subject=subject,
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL,
        )
        shipment.email_sent = True
        shipment.save()
    
    modeladmin.message_user(request, f'Notification emails sent successfully to {len(queryset)} shipments.')


def reset_email_status(modeladmin, request, queryset):
    """Reset email sent status to False"""
    queryset.update(email_sent=False)
    modeladmin.message_user(request, f'Email status reset for {len(queryset)} shipments.')


def sync_from_imports(modeladmin, request, queryset=None):
    """Sync data from imports app to consolidated admin"""
    imports = Import.objects.filter(
        Q(consolidatedshipment__isnull=True) | 
        Q(consolidatedshipment__import_reference__isnull=True)
    )
    
    synced_count = 0
    for import_obj in imports:
        # Check if consolidated shipment already exists
        consolidated, created = ConsolidatedShipment.objects.get_or_create(
            import_reference=import_obj,
            defaults={
                'shipment_type': ShipmentType.IMPORT,
                'customer': import_obj.customer,
                'sender': import_obj.sender,
                'destination': import_obj.destination,
                'phone_no': import_obj.phone_no,
                'weight': import_obj.weight,
                'weight_unit': import_obj.weight_unit,
                'length': import_obj.lenght,  # Note: original has typo 'lenght'
                'breadth': import_obj.breadth,
                'height': import_obj.height,
                'volumetric_weight': import_obj.volumentary_weight,
                'volumetric_weight_unit': import_obj.volumentary_weight_unit,
                'shipping_fee': import_obj.import_fee,
                'tracking_no': import_obj.tracking_no,
                'payment_status': import_obj.payment_status,
                'payment_reference': import_obj.payment_reference,
                'shipped_at': import_obj.shipped_at,
                'shipping_status': import_obj.shipping_status,
                'attention': import_obj.attention,
                'email_sent': import_obj.email_sent,
            }
        )
        
        if created:
            synced_count += 1
            # Sync items
            for item in ImportItem.objects.filter(imports=import_obj):
                ConsolidatedItem.objects.get_or_create(
                    import_item_reference=item,
                    defaults={
                        'shipment': consolidated,
                        'name': item.name,
                        'quantity': item.quantity,
                        'price': item.price,
                        'total_price': item.total_price,
                        'weight': item.weight,
                    }
                )
    
    modeladmin.message_user(request, f'Synced {synced_count} import records to consolidated admin.')


def sync_from_exports(modeladmin, request, queryset=None):
    """Sync data from exports app to consolidated admin"""
    exports = Export.objects.filter(
        Q(consolidatedshipment__isnull=True) | 
        Q(consolidatedshipment__export_reference__isnull=True)
    )
    
    synced_count = 0
    for export_obj in exports:
        # Check if consolidated shipment already exists
        consolidated, created = ConsolidatedShipment.objects.get_or_create(
            export_reference=export_obj,
            defaults={
                'shipment_type': ShipmentType.EXPORT,
                'customer': export_obj.customer,
                'sender': export_obj.sender,
                'destination': export_obj.destination,
                'phone_no': export_obj.phone_no,
                'weight': export_obj.weight,
                'weight_unit': export_obj.weight_unit,
                'length': export_obj.lenght,  # Note: original has typo 'lenght'
                'breadth': export_obj.breadth,
                'height': export_obj.height,
                'volumetric_weight': export_obj.volumentary_weight,
                'volumetric_weight_unit': export_obj.volumentary_weight_unit,
                'shipping_fee': export_obj.export_fee,
                'tracking_no': export_obj.tracking_no,
                'payment_status': export_obj.payment_status,
                'payment_reference': export_obj.payment_reference,
                'shipped_at': export_obj.shipped_at,
                'shipping_status': export_obj.shipping_status,
                'tracking_url': export_obj.tracking_url,
                'attention': export_obj.attention,
                'email_sent': export_obj.email_sent,
            }
        )
        
        if created:
            synced_count += 1
            # Sync items
            for item in ExportItem.objects.filter(export=export_obj):
                ConsolidatedItem.objects.get_or_create(
                    export_item_reference=item,
                    defaults={
                        'shipment': consolidated,
                        'name': item.name,
                        'quantity': item.quantity,
                        'price': item.price,
                        'total_price': item.total_price,
                        'weight': item.weight,
                    }
                )
    
    modeladmin.message_user(request, f'Synced {synced_count} export records to consolidated admin.')


# Custom actions
send_consolidated_notification_email.short_description = "Send notification emails"
reset_email_status.short_description = "Reset email sent status"
sync_from_imports.short_description = "Sync from Imports app"
sync_from_exports.short_description = "Sync from Exports app"


@admin.register(ConsolidatedShipment)
class ConsolidatedShipmentAdmin(admin.ModelAdmin):
    form = ConsolidatedShipmentForm
    inlines = [ConsolidatedItemInline]
    
    list_display = (
        'tracking_no', 'shipment_type_badge', 'customer_name', 'sender_location', 
        'destination_location', 'display_weight', 'display_volumetric_weight',
        'shipping_fee', 'payment_status_badge', 'shipping_status_badge', 'email_sent_badge'
    )
    
    list_filter = (
        'shipment_type', 'payment_status', 'shipping_status', 'email_sent',
        'created_on', 'shipped_at'
    )
    
    search_fields = (
        'tracking_no', 'customer__email', 'customer__first_name', 
        'customer__last_name', 'payment_reference', 'phone_no',
    )
    
    readonly_fields = (
        'tracking_no', 'payment_reference', 'display_weight', 
        'display_volumetric_weight', 'created_on', 'updated_on'
    )
    
    actions = [
        send_consolidated_notification_email, 
        reset_email_status,
        sync_from_imports,
        sync_from_exports
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('shipment_type', 'customer', 'tracking_no', 'phone_no')
        }),
        ('Addresses', {
            'fields': ('sender', 'destination')
        }),
        ('Dimensions & Weight', {
            'fields': (
                ('weight', 'weight_unit'),
                ('length', 'breadth', 'height'),
                ('display_volumetric_weight', 'volumetric_weight_unit')
            )
        }),
        ('Payment & Shipping', {
            'fields': (
                'shipping_fee', 'payment_status', 'payment_reference',
                'shipping_status', 'shipped_at', 'tracking_url'
            )
        }),
        ('Additional Information', {
            'fields': ('attention', 'email_sent')
        }),
        ('References', {
            'fields': ('import_reference', 'export_reference'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )

    def shipment_type_badge(self, obj):
        color = 'blue' if obj.is_import else 'green'
        return create_badge(color, obj.get_shipment_type_display())
    shipment_type_badge.short_description = 'Type'

    def customer_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}"
    customer_name.short_description = 'Customer'
    customer_name.admin_order_field = 'customer__last_name'

    def sender_location(self, obj):
        if obj.sender:
            return f"{obj.sender.city}, {obj.sender.country}"
        return "N/A"
    sender_location.short_description = 'From'

    def destination_location(self, obj):
        if obj.destination:
            return f"{obj.destination.city}, {obj.destination.country}"
        return "N/A"
    destination_location.short_description = 'To'

    def display_weight(self, obj):
        return obj.get_weight
    display_weight.short_description = "Weight"

    def display_volumetric_weight(self, obj):
        return obj.get_volumetric_weight
    display_volumetric_weight.short_description = "Vol. Weight"

    def payment_status_badge(self, obj):
        color = 'green' if obj.payment_status == 'Paid' else 'red'
        return create_badge(color, obj.payment_status)
    payment_status_badge.short_description = 'Payment'

    def shipping_status_badge(self, obj):
        colors = {
            'Pending': 'orange',
            'Processing': 'blue',
            'Shipped': 'green',
            'Delivered': 'darkgreen',
            'Cancelled': 'red'
        }
        color = colors.get(obj.shipping_status, 'gray')
        return create_badge(color, obj.shipping_status)
    shipping_status_badge.short_description = 'Status'

    def email_sent_badge(self, obj):
        color = 'green' if obj.email_sent else 'red'
        text = 'Sent' if obj.email_sent else 'Not Sent'
        return create_badge(color, text)
    email_sent_badge.short_description = 'Email'


@admin.register(ConsolidatedItem)
class ConsolidatedItemAdmin(admin.ModelAdmin):
    list_display = (
        'shipment_info', 'name', 'quantity', 'price', 'total_price', 'weight'
    )
    
    list_filter = (
        'shipment__shipment_type', 'shipment__shipping_status', 'created_on'
    )
    
    search_fields = (
        'name', 'shipment__tracking_no', 'shipment__customer__first_name',
        'shipment__customer__last_name', 'shipment__customer__email'
    )
    
    readonly_fields = ('total_price', 'created_on', 'updated_on')

    def shipment_info(self, obj):
        return format_html(
            '{} - {} ({})',
            obj.shipment.get_shipment_type_display(),
            obj.shipment.tracking_no,
            obj.shipment.customer
        )
    shipment_info.short_description = 'Shipment'
    shipment_info.admin_order_field = 'shipment__tracking_no'
