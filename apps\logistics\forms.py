from django import forms
from .models import Logistic


class LogisticUpdateForm(forms.ModelForm):

    class Meta:
        model = Logistic
        fields = ['company', 'sender', 'receiver_name', 'receiver_address', 'receiver_city', 'receiver_state', 'phone_no', 'delivery_fee', 'tracking_no', 'delivery_status']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['company'].widget.attrs.update({'class':"form-control"})
        self.fields['sender'].widget.attrs.update({'class':"form-control"})
        self.fields['receiver_name'].widget.attrs.update({'class':"form-control"})
        self.fields['receiver_address'].widget.attrs.update({'class':"form-control"})
        self.fields['receiver_city'].widget.attrs.update({'class':"form-control"})
        self.fields['receiver_state'].widget.attrs.update({'class':"form-control"})
        self.fields['phone_no'].widget.attrs.update({'class':"form-control"})
        self.fields['delivery_fee'].widget.attrs.update({'class':"form-control"})
        self.fields['tracking_no'].widget.attrs.update({'class':"form-control"})
        self.fields['delivery_status'].widget.attrs.update({'class': "form-control"})

        # Disable certain fields from dispatcher access
        self.fields['company'].disabled=True
        self.fields['sender'].disabled=True
        self.fields['receiver_name'].disabled=True
        self.fields['receiver_address'].disabled=True
        self.fields['receiver_city'].disabled=True
        self.fields['receiver_state'].disabled=True
        self.fields['phone_no'].disabled=True
        self.fields['delivery_fee'].disabled=True
        self.fields['tracking_no'].disabled=True
        
    
    